import requests

api_key = "p7KFyMJyUNKqwNDUX4kdKwEEpc5eTAA1"  # ← yaha apna key daalo

headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

data = {
    "model": "mistral-tiny",
    "messages": [
        {"role": "user", "content": "Hello"}
    ]
}

response = requests.post(
    "https://api.mistral.ai/v1/chat/completions",
    headers=headers,
    json=data
)

if response.status_code == 200:
    print("✅ API key is valid!")
    print("Response:", response.json())
elif response.status_code == 401:
    print("❌ Invalid API key!")
else:
    print(f"⚠️ Something went wrong. Status code: {response.status_code}")
    print("Details:", response.text)
