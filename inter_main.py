import pyautogui
import keyboard
import time
import base64
import io
import requests
import json
from PIL import Image
import threading
import os
import sqlite3
from datetime import datetime
import speech_recognition as sr
import pyttsx3
import pyaudio
import wave
import numpy as np
from collections import defaultdict, deque
import re
import tempfile
import whisper
from pydub import AudioSegment
from pydub.silence import split_on_silence
import webrtcvad
import logging
import queue
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from tkinter import ttk
import psutil
import ctypes
from ctypes import wintypes
import configparser
import hashlib
import pickle
from threading import Lock, Event
import signal
import sys

# Configure enhanced logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stealth_assistant.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Windows API for Caps Lock detection
user32 = ctypes.windll.user32
kernel32 = ctypes.windll.kernel32

class ConfigManager:
    """Enhanced configuration management system"""
    def __init__(self, config_file='stealth_config.ini'):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_config()

    def load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file)
            else:
                self.create_default_config()
        except Exception as e:
            logger.error(f"Config loading error: {e}")
            self.create_default_config()

    def create_default_config(self):
        """Create default configuration"""
        self.config['AUDIO'] = {
            'sample_rate': '44100',
            'channels': '2',
            'chunk_size': '1024',
            'silence_threshold': '500',
            'vad_aggressiveness': '2'
        }

        self.config['SYSTEM'] = {
            'screen_check_interval': '2.0',
            'question_threshold': '5.0',
            'max_audio_buffer': '300',
            'enable_continuous_playback': 'true',
            'caps_lock_aware': 'true'
        }

        self.config['AI'] = {
            'temperature': '0.7',
            'max_tokens': '1500',
            'cache_enabled': 'true',
            'cache_size': '100'
        }

        self.config['UI'] = {
            'show_audio_visualization': 'true',
            'show_status_dashboard': 'true',
            'enable_notifications': 'true'
        }

        self.save_config()

    def save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                self.config.write(f)
        except Exception as e:
            logger.error(f"Config saving error: {e}")

    def get(self, section, key, fallback=None):
        """Get configuration value"""
        try:
            return self.config.get(section, key, fallback=fallback)
        except:
            return fallback

    def getboolean(self, section, key, fallback=False):
        """Get boolean configuration value"""
        try:
            return self.config.getboolean(section, key, fallback=fallback)
        except:
            return fallback

    def getfloat(self, section, key, fallback=0.0):
        """Get float configuration value"""
        try:
            return self.config.getfloat(section, key, fallback=fallback)
        except:
            return fallback

    def getint(self, section, key, fallback=0):
        """Get integer configuration value"""
        try:
            return self.config.getint(section, key, fallback=fallback)
        except:
            return fallback

class SystemMonitor:
    """Enhanced system monitoring and status tracking"""
    def __init__(self):
        self.caps_lock_state = False
        self.system_stats = {}
        self.lock = Lock()

    def get_caps_lock_state(self):
        """Get current Caps Lock state"""
        try:
            # VK_CAPITAL = 0x14
            state = user32.GetKeyState(0x14)
            return bool(state & 1)
        except:
            return False

    def update_caps_lock_state(self):
        """Update and return Caps Lock state"""
        with self.lock:
            self.caps_lock_state = self.get_caps_lock_state()
            return self.caps_lock_state

    def get_system_stats(self):
        """Get comprehensive system statistics"""
        try:
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()

            stats = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available': memory.available // (1024*1024),  # MB
                'caps_lock': self.update_caps_lock_state(),
                'timestamp': time.time()
            }

            with self.lock:
                self.system_stats = stats

            return stats
        except Exception as e:
            logger.error(f"System stats error: {e}")
            return {}

class AudioStreamer:
    """Enhanced audio streaming and playback system"""
    def __init__(self, config_manager):
        self.config = config_manager
        self.audio_queue = queue.Queue(maxsize=1000)
        self.playback_queue = queue.Queue(maxsize=100)
        self.is_streaming = False
        self.is_playing = False
        self.stream_thread = None
        self.playback_thread = None
        self.audio_data_buffer = deque(maxlen=1000)
        self.lock = Lock()

    def start_continuous_streaming(self, audio_device_index, sample_rate, channels):
        """Start continuous audio streaming"""
        if self.is_streaming:
            return

        self.is_streaming = True
        self.stream_thread = threading.Thread(
            target=self._stream_worker,
            args=(audio_device_index, sample_rate, channels),
            daemon=True
        )
        self.stream_thread.start()
        logger.info("Continuous audio streaming started")

    def _stream_worker(self, device_index, sample_rate, channels):
        """Audio streaming worker thread"""
        try:
            audio = pyaudio.PyAudio()
            chunk_size = self.config.getint('AUDIO', 'chunk_size', 1024)

            stream = audio.open(
                format=pyaudio.paInt16,
                channels=channels,
                rate=sample_rate,
                input=True,
                input_device_index=device_index,
                frames_per_buffer=chunk_size
            )

            while self.is_streaming:
                try:
                    data = stream.read(chunk_size, exception_on_overflow=False)

                    # Add to queue for processing
                    if not self.audio_queue.full():
                        self.audio_queue.put(data)

                    # Add to buffer for visualization
                    with self.lock:
                        self.audio_data_buffer.append(data)

                    time.sleep(0.001)  # Small delay to prevent CPU overload

                except Exception as e:
                    logger.error(f"Audio streaming error: {e}")
                    time.sleep(0.1)

            stream.stop_stream()
            stream.close()
            audio.terminate()

        except Exception as e:
            logger.error(f"Audio stream worker error: {e}")

    def start_continuous_playback(self):
        """Start continuous audio playback"""
        if self.is_playing:
            return

        self.is_playing = True
        self.playback_thread = threading.Thread(
            target=self._playback_worker,
            daemon=True
        )
        self.playback_thread.start()
        logger.info("Continuous audio playback started")

    def _playback_worker(self):
        """Audio playback worker thread"""
        try:
            while self.is_playing:
                try:
                    if not self.playback_queue.empty():
                        audio_data = self.playback_queue.get(timeout=1)
                        # Process audio playback here
                        # This could be used for audio feedback or notifications
                        pass
                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"Audio playback error: {e}")
                    time.sleep(0.1)
        except Exception as e:
            logger.error(f"Audio playback worker error: {e}")

    def get_audio_data(self, timeout=0.1):
        """Get audio data from stream"""
        try:
            return self.audio_queue.get(timeout=timeout)
        except queue.Empty:
            return None

    def get_visualization_data(self):
        """Get audio data for visualization"""
        with self.lock:
            if self.audio_data_buffer:
                # Get recent audio data for visualization
                recent_data = list(self.audio_data_buffer)[-10:]  # Last 10 chunks
                if recent_data:
                    combined = b''.join(recent_data)
                    audio_array = np.frombuffer(combined, dtype=np.int16)
                    return audio_array
        return np.array([])

    def stop_streaming(self):
        """Stop audio streaming"""
        self.is_streaming = False
        if self.stream_thread:
            self.stream_thread.join(timeout=2)

    def stop_playback(self):
        """Stop audio playback"""
        self.is_playing = False
        if self.playback_thread:
            self.playback_thread.join(timeout=2)

class StealthInterviewAssistant:
    def __init__(self, gemini_api_key):
        self.api_key = gemini_api_key
        self.gemini_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={gemini_api_key}"

        # Initialize enhanced systems
        self.config = ConfigManager()
        self.system_monitor = SystemMonitor()
        self.audio_streamer = AudioStreamer(self.config)

        # Audio capture settings (enhanced)
        self.stereo_mix_enabled = False
        self.audio_buffer = deque(maxlen=self.config.getint('SYSTEM', 'max_audio_buffer', 300))
        self.is_recording = False
        self.last_question_time = 0
        self.question_threshold = self.config.getfloat('SYSTEM', 'question_threshold', 5.0)

        # Screen monitoring (enhanced)
        self.screen_monitoring = False
        self.last_screen_check = 0
        self.screen_change_threshold = self.config.getfloat('SYSTEM', 'screen_check_interval', 2.0)

        # AI Analysis (enhanced)
        self.whisper_model = None
        self.question_history = []
        self.answer_cache = {}
        self.cache_lock = Lock()

        # Performance tracking (enhanced)
        self.setup_database()
        self.performance_metrics = defaultdict(int)
        self.status_dashboard = None

        # Voice Activity Detection (enhanced)
        vad_level = self.config.getint('AUDIO', 'vad_aggressiveness', 2)
        self.vad = webrtcvad.Vad(vad_level)

        # Interview context (enhanced)
        self.interview_context = {
            'company': '',
            'role': '',
            'interviewer_name': '',
            'interview_type': 'general'
        }

        # Thread management
        self.threads = []
        self.shutdown_event = Event()

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        print("🕵️ Enhanced Stealth Interview Assistant Initialized!")
        print("✨ New Features: Continuous Audio, Caps Lock Support, Advanced UI")

        self.initialize_enhanced_systems()
        self.show_enhanced_menu()

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        print(f"\n🛑 Received signal {signum}, shutting down gracefully...")
        self.shutdown_event.set()
        self.cleanup_resources()
        sys.exit(0)

    def cleanup_resources(self):
        """Clean up all resources"""
        try:
            print("🧹 Cleaning up resources...")

            # Stop audio streaming
            self.audio_streamer.stop_streaming()
            self.audio_streamer.stop_playback()

            # Stop monitoring
            self.is_recording = False
            self.screen_monitoring = False

            # Wait for threads to finish
            for thread in self.threads:
                if thread.is_alive():
                    thread.join(timeout=2)

            # Close database connection
            if hasattr(self, 'conn'):
                self.conn.close()

            # Close status dashboard
            if self.status_dashboard:
                try:
                    self.status_dashboard.destroy()
                except:
                    pass

            print("✅ Resources cleaned up successfully")

        except Exception as e:
            logger.error(f"Cleanup error: {e}")

    def initialize_enhanced_systems(self):
        """Initialize all enhanced systems"""
        print("🚀 Initializing enhanced systems...")

        # Initialize audio system
        self.initialize_audio_system()

        # Initialize status dashboard if enabled
        if self.config.getboolean('UI', 'show_status_dashboard', True):
            self.initialize_status_dashboard()

        # Start system monitoring
        self.start_system_monitoring()

        print("✅ Enhanced systems initialized!")

    def initialize_audio_system(self):
        """Initialize enhanced audio capture system with continuous streaming"""
        try:
            # Load Whisper model for better speech recognition
            print("🤖 Loading Whisper AI model...")
            self.whisper_model = whisper.load_model("base")
            print("✅ Whisper model loaded!")

            # Setup PyAudio for stereo mix capture
            self.audio = pyaudio.PyAudio()

            # Find stereo mix device
            self.find_stereo_mix_device()

            if self.stereo_mix_enabled:
                print("🎧 Stereo Mix detected and ready!")

                # Start continuous audio streaming if enabled
                if self.config.getboolean('SYSTEM', 'enable_continuous_playback', True):
                    self.audio_streamer.start_continuous_streaming(
                        self.stereo_mix_device_index,
                        self.sample_rate,
                        self.channels
                    )
                    self.audio_streamer.start_continuous_playback()
                    print("🔄 Continuous audio streaming enabled!")
            else:
                print("⚠️ Stereo Mix not detected. Manual setup may be required.")

        except Exception as e:
            print(f"❌ Audio system initialization error: {e}")
            print("📝 Make sure you have enabled Stereo Mix in Windows Sound settings")

    def initialize_status_dashboard(self):
        """Initialize real-time status dashboard"""
        try:
            print("📊 Initializing status dashboard...")

            # Create dashboard in separate thread
            dashboard_thread = threading.Thread(
                target=self._create_status_dashboard,
                daemon=True
            )
            dashboard_thread.start()
            self.threads.append(dashboard_thread)

            print("✅ Status dashboard initialized!")

        except Exception as e:
            logger.error(f"Dashboard initialization error: {e}")

    def _create_status_dashboard(self):
        """Create and run status dashboard"""
        try:
            root = tk.Tk()
            root.title("Stealth Assistant - Status Dashboard")
            root.geometry("800x600")
            root.configure(bg='#1e1e1e')

            # Create main frame
            main_frame = ttk.Frame(root)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Status labels
            self.status_labels = {}

            # System status
            system_frame = ttk.LabelFrame(main_frame, text="System Status")
            system_frame.pack(fill=tk.X, pady=5)

            self.status_labels['caps_lock'] = ttk.Label(system_frame, text="Caps Lock: OFF")
            self.status_labels['caps_lock'].pack(anchor=tk.W)

            self.status_labels['cpu'] = ttk.Label(system_frame, text="CPU: 0%")
            self.status_labels['cpu'].pack(anchor=tk.W)

            self.status_labels['memory'] = ttk.Label(system_frame, text="Memory: 0%")
            self.status_labels['memory'].pack(anchor=tk.W)

            # Audio status
            audio_frame = ttk.LabelFrame(main_frame, text="Audio Status")
            audio_frame.pack(fill=tk.X, pady=5)

            self.status_labels['audio_streaming'] = ttk.Label(audio_frame, text="Streaming: OFF")
            self.status_labels['audio_streaming'].pack(anchor=tk.W)

            self.status_labels['questions_detected'] = ttk.Label(audio_frame, text="Questions: 0")
            self.status_labels['questions_detected'].pack(anchor=tk.W)

            # Audio visualization
            if self.config.getboolean('UI', 'show_audio_visualization', True):
                viz_frame = ttk.LabelFrame(main_frame, text="Audio Visualization")
                viz_frame.pack(fill=tk.BOTH, expand=True, pady=5)

                # Create matplotlib figure
                fig, ax = plt.subplots(figsize=(8, 3))
                fig.patch.set_facecolor('#1e1e1e')
                ax.set_facecolor('#2d2d2d')
                ax.set_xlim(0, 1000)
                ax.set_ylim(-32768, 32767)
                ax.set_title("Real-time Audio Waveform", color='white')

                # Create canvas
                canvas = FigureCanvasTkAgg(fig, viz_frame)
                canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

                # Animation function
                def update_audio_viz(frame):
                    try:
                        audio_data = self.audio_streamer.get_visualization_data()
                        if len(audio_data) > 0:
                            ax.clear()
                            ax.plot(audio_data[:1000], color='#00ff00', linewidth=1)
                            ax.set_xlim(0, 1000)
                            ax.set_ylim(-32768, 32767)
                            ax.set_title("Real-time Audio Waveform", color='white')
                            ax.tick_params(colors='white')
                    except Exception as e:
                        logger.error(f"Audio visualization error: {e}")

                # Start animation
                ani = animation.FuncAnimation(fig, update_audio_viz, interval=100, blit=False)

            # Update status periodically
            def update_status():
                try:
                    stats = self.system_monitor.get_system_stats()

                    # Update labels
                    caps_state = "ON" if stats.get('caps_lock', False) else "OFF"
                    self.status_labels['caps_lock'].config(text=f"Caps Lock: {caps_state}")

                    self.status_labels['cpu'].config(text=f"CPU: {stats.get('cpu_percent', 0):.1f}%")
                    self.status_labels['memory'].config(text=f"Memory: {stats.get('memory_percent', 0):.1f}%")

                    streaming_state = "ON" if self.audio_streamer.is_streaming else "OFF"
                    self.status_labels['audio_streaming'].config(text=f"Streaming: {streaming_state}")

                    self.status_labels['questions_detected'].config(text=f"Questions: {len(self.question_history)}")

                    # Schedule next update
                    root.after(1000, update_status)

                except Exception as e:
                    logger.error(f"Status update error: {e}")
                    root.after(1000, update_status)

            # Start status updates
            update_status()

            # Store reference
            self.status_dashboard = root

            # Run dashboard
            root.mainloop()

        except Exception as e:
            logger.error(f"Dashboard creation error: {e}")

    def start_system_monitoring(self):
        """Start system monitoring thread"""
        try:
            monitor_thread = threading.Thread(
                target=self._system_monitor_worker,
                daemon=True
            )
            monitor_thread.start()
            self.threads.append(monitor_thread)

            print("🔍 System monitoring started!")

        except Exception as e:
            logger.error(f"System monitoring error: {e}")

    def _system_monitor_worker(self):
        """System monitoring worker thread"""
        while not self.shutdown_event.is_set():
            try:
                # Update system stats
                self.system_monitor.get_system_stats()

                # Check for Caps Lock changes
                caps_state = self.system_monitor.update_caps_lock_state()

                # Log significant changes
                if hasattr(self, '_last_caps_state') and self._last_caps_state != caps_state:
                    logger.info(f"Caps Lock state changed: {'ON' if caps_state else 'OFF'}")

                self._last_caps_state = caps_state

                time.sleep(1)  # Update every second

            except Exception as e:
                logger.error(f"System monitor worker error: {e}")
                time.sleep(5)

    def find_stereo_mix_device(self):
        """Find and configure stereo mix audio device"""
        try:
            device_count = self.audio.get_device_count()
            
            for i in range(device_count):
                device_info = self.audio.get_device_info_by_index(i)
                device_name = device_info['name'].lower()
                
                # Look for stereo mix or similar devices
                if any(keyword in device_name for keyword in 
                       ['stereo mix', 'wave out mix', 'what u hear', 'speakers', 'loopback']):
                    
                    if device_info['maxInputChannels'] > 0:
                        self.stereo_mix_device_index = i
                        self.stereo_mix_enabled = True
                        
                        print(f"🎯 Found audio device: {device_info['name']}")
                        print(f"📊 Sample Rate: {int(device_info['defaultSampleRate'])}Hz")
                        print(f"🔊 Input Channels: {device_info['maxInputChannels']}")
                        
                        # Configure audio parameters
                        self.sample_rate = int(device_info['defaultSampleRate'])
                        self.channels = min(device_info['maxInputChannels'], 2)
                        self.chunk_size = 1024
                        
                        break
                        
        except Exception as e:
            print(f"❌ Error finding stereo mix: {e}")
            self.stereo_mix_enabled = False

    def setup_database(self):
        """Setup enhanced database for stealth operations"""
        self.conn = sqlite3.connect('stealth_interview.db', check_same_thread=False)
        cursor = self.conn.cursor()
        
        # Interview sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stealth_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                company TEXT,
                role TEXT,
                question_audio TEXT,
                question_text TEXT,
                question_type TEXT,
                ai_answer TEXT,
                confidence_score REAL,
                response_time REAL,
                screen_context TEXT,
                interviewer_pattern TEXT
            )
        ''')
        
        # Audio patterns table for interviewer recognition
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS interviewer_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                interviewer_id TEXT,
                voice_pattern BLOB,
                question_style TEXT,
                common_followups TEXT,
                difficulty_level TEXT
            )
        ''')
        
        # Real-time insights table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS realtime_insights (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                insight_type TEXT,
                content TEXT,
                urgency_level INTEGER
            )
        ''')
        
        self.conn.commit()

    def show_enhanced_menu(self):
        """Display enhanced stealth mode options"""
        print("\n" + "🕵️" * 35)
        print("🎯 ENHANCED STEALTH INTERVIEW ASSISTANT")
        print("🕵️" * 35)
        print("1. 🎧 Enhanced Audio Monitoring (Continuous Stream)")
        print("2. 🖥️  Advanced Screen Analysis (OCR + AI)")
        print("3. 🤖 Full Stealth Mode (Audio + Screen + Dashboard)")
        print("4. 📊 Interview Context Setup")
        print("5. 🔍 Question Pattern Analysis")
        print("6. 📈 Real-time Performance Dashboard")
        print("7. 🛡️  Emergency Assistance Mode")
        print("8. 🎮 Interviewer Behavior Analysis")
        print("9. ⚙️  Audio System Diagnostics")
        print("A. 🔄 Continuous Audio Streaming Control")
        print("B. 📱 Status Dashboard Control")
        print("C. ⚙️  Configuration Management")
        print("0. 🚀 START ENHANCED STEALTH ASSISTANT")
        print("\n⚡ ENHANCED HOTKEYS (During Operation):")
        print("Caps Lock - Manual screen analysis (Caps Lock aware)")
        print("F11 - Emergency help")
        print("F12 - Show live insights")
        print("Ctrl+Shift+Q - Quick question lookup")
        print("Ctrl+Shift+S - Toggle audio streaming")
        print("Ctrl+Shift+D - Toggle status dashboard")
        print("Ctrl+Shift+R - Restart audio system")
        print("Ctrl+C - Stop assistant")
        print("\n✨ NEW FEATURES:")
        print("🔄 Continuous audio streaming and playback")
        print("🔒 Caps Lock state detection and handling")
        print("📊 Real-time status dashboard with audio visualization")
        print("💾 Enhanced caching and performance optimization")
        print("🎯 Advanced voice activity detection")
        print("⚙️  Comprehensive configuration management")
        print("🕵️" * 35)

    def show_stealth_menu(self):
        """Legacy method - redirects to enhanced menu"""
        self.show_enhanced_menu()

    def start_audio_monitoring(self):
        """Start enhanced continuous audio monitoring with streaming support"""
        if not self.stereo_mix_enabled:
            print("❌ Stereo Mix not available!")
            return

        self.is_recording = True
        print("🎧 Starting enhanced audio monitoring...")
        print("🔊 Listening to system audio with continuous streaming...")

        try:
            # Use the enhanced audio streamer if available
            if self.audio_streamer.is_streaming:
                print("✅ Using continuous audio stream!")
                self._monitor_from_stream()
            else:
                print("📡 Starting traditional audio monitoring...")
                self._monitor_traditional()

        except Exception as e:
            print(f"❌ Audio monitoring error: {e}")
            print("💡 Try: Check Windows Sound settings > Recording > Enable Stereo Mix")

    def _monitor_from_stream(self):
        """Monitor audio from continuous stream"""
        audio_buffer = []
        silence_threshold = self.config.getint('AUDIO', 'silence_threshold', 500)

        while self.is_recording and not self.shutdown_event.is_set():
            try:
                # Get audio data from stream
                data = self.audio_streamer.get_audio_data(timeout=0.1)

                if data:
                    audio_buffer.append(data)

                    # Convert to numpy array for analysis
                    audio_data = np.frombuffer(data, dtype=np.int16)
                    volume = np.sqrt(np.mean(audio_data**2))

                    # Enhanced voice activity detection
                    if self._is_voice_activity(data, volume, silence_threshold):
                        # Potential speech detected
                        if len(audio_buffer) > 30:  # ~3 seconds of audio
                            self.process_enhanced_audio_chunk(audio_buffer.copy())
                            audio_buffer = audio_buffer[-10:]  # Keep overlap

                    # Maintain buffer size
                    if len(audio_buffer) > 100:  # ~10 seconds max
                        audio_buffer = audio_buffer[-50:]

                time.sleep(0.01)  # Small delay to prevent CPU overload

            except Exception as audio_error:
                logger.error(f"Stream audio processing error: {audio_error}")
                time.sleep(0.1)

    def _monitor_traditional(self):
        """Traditional audio monitoring method"""
        try:
            # Open audio stream for stereo mix
            stream = self.audio.open(
                format=pyaudio.paInt16,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.stereo_mix_device_index,
                frames_per_buffer=self.chunk_size
            )

            print("✅ Traditional audio stream active!")

            audio_buffer = []
            silence_threshold = self.config.getint('AUDIO', 'silence_threshold', 500)

            while self.is_recording and not self.shutdown_event.is_set():
                try:
                    # Read audio data
                    data = stream.read(self.chunk_size, exception_on_overflow=False)
                    audio_buffer.append(data)

                    # Convert to numpy array for analysis
                    audio_data = np.frombuffer(data, dtype=np.int16)
                    volume = np.sqrt(np.mean(audio_data**2))

                    # Enhanced voice activity detection
                    if self._is_voice_activity(data, volume, silence_threshold):
                        # Potential speech detected
                        if len(audio_buffer) > 30:  # ~3 seconds of audio
                            self.process_enhanced_audio_chunk(audio_buffer.copy())
                            audio_buffer = audio_buffer[-10:]  # Keep overlap

                    # Maintain buffer size
                    if len(audio_buffer) > 100:  # ~10 seconds max
                        audio_buffer = audio_buffer[-50:]

                    time.sleep(0.01)  # Small delay to prevent CPU overload

                except Exception as audio_error:
                    logger.error(f"Traditional audio processing error: {audio_error}")
                    time.sleep(0.1)

            stream.stop_stream()
            stream.close()

        except Exception as e:
            logger.error(f"Traditional audio monitoring error: {e}")

    def _is_voice_activity(self, audio_data, volume, threshold):
        """Enhanced voice activity detection"""
        try:
            # Basic volume check
            if volume < threshold:
                return False

            # Use WebRTC VAD for better detection
            if len(audio_data) >= 320:  # Minimum frame size for VAD
                # Convert to appropriate format for VAD
                frame_size = 320  # 20ms at 16kHz
                if len(audio_data) >= frame_size * 2:  # 16-bit samples
                    frame = audio_data[:frame_size * 2]
                    try:
                        # VAD expects 16kHz, but we'll try with our sample rate
                        is_speech = self.vad.is_speech(frame, self.sample_rate)
                        return is_speech
                    except:
                        # Fallback to volume-based detection
                        return volume > threshold

            return volume > threshold

        except Exception as e:
            logger.error(f"Voice activity detection error: {e}")
            return volume > threshold

    def process_enhanced_audio_chunk(self, audio_buffer):
        """Enhanced audio chunk processing with better performance and caching"""
        try:
            # Combine audio chunks
            audio_data = b''.join(audio_buffer)

            # Create hash for caching
            audio_hash = hashlib.md5(audio_data).hexdigest()

            # Check cache first
            with self.cache_lock:
                if audio_hash in self.answer_cache:
                    cached_result = self.answer_cache[audio_hash]
                    if cached_result.get('transcription'):
                        print(f"\n💾 CACHED TRANSCRIPTION: {cached_result['transcription']}")
                        if self.is_likely_question(cached_result['transcription']):
                            self.handle_detected_question(cached_result['transcription'], 'audio_cached')
                        return

            # Save temporary audio file with better naming
            timestamp = int(time.time() * 1000)
            temp_filename = f"temp_audio_{timestamp}.wav"

            try:
                # Write WAV file with enhanced parameters
                with wave.open(temp_filename, 'wb') as wav_file:
                    wav_file.setnchannels(self.channels)
                    wav_file.setsampwidth(self.audio.get_sample_size(pyaudio.paInt16))
                    wav_file.setframerate(self.sample_rate)
                    wav_file.writeframes(audio_data)

                # Use Whisper for transcription with enhanced options
                result = self.whisper_model.transcribe(
                    temp_filename,
                    language='en',  # Optimize for English
                    task='transcribe',
                    temperature=0.0,  # More deterministic
                    best_of=1,  # Faster processing
                    beam_size=1,  # Faster processing
                    patience=1.0,
                    suppress_tokens=[-1],  # Suppress common noise tokens
                    initial_prompt="This is an interview question or conversation."
                )

                transcribed_text = result["text"].strip()
                confidence = result.get("confidence", 0.8)

                # Cache the result
                with self.cache_lock:
                    self.answer_cache[audio_hash] = {
                        'transcription': transcribed_text,
                        'confidence': confidence,
                        'timestamp': time.time()
                    }

                    # Limit cache size
                    if len(self.answer_cache) > self.config.getint('AI', 'cache_size', 100):
                        # Remove oldest entries
                        oldest_key = min(self.answer_cache.keys(),
                                       key=lambda k: self.answer_cache[k]['timestamp'])
                        del self.answer_cache[oldest_key]

                # Clean up temp file
                if os.path.exists(temp_filename):
                    os.unlink(temp_filename)

                # Process transcription
                if len(transcribed_text) > 10 and confidence > 0.5:  # Minimum quality threshold
                    if self.is_likely_question(transcribed_text):
                        print(f"\n🎤 DETECTED QUESTION (Confidence: {confidence:.2f}): {transcribed_text}")
                        self.handle_detected_question(transcribed_text, 'audio')
                    else:
                        logger.debug(f"Audio transcribed but not a question: {transcribed_text[:50]}...")
                else:
                    logger.debug(f"Low quality transcription ignored: {transcribed_text[:30]}...")

            except Exception as file_error:
                logger.error(f"Audio file processing error: {file_error}")
                # Clean up temp file on error
                if os.path.exists(temp_filename):
                    try:
                        os.unlink(temp_filename)
                    except:
                        pass

        except Exception as e:
            logger.error(f"Enhanced audio chunk processing error: {e}")

    def process_audio_chunk(self, audio_buffer):
        """Legacy method - redirects to enhanced version"""
        self.process_enhanced_audio_chunk(audio_buffer)

    def is_likely_question(self, text):
        """Determine if text is likely a question"""
        text_lower = text.lower().strip()
        
        # Question indicators
        question_words = ['what', 'how', 'why', 'when', 'where', 'who', 'which', 'can you', 'tell me', 'describe', 'explain']
        question_patterns = [
            r'\?',  # Ends with question mark
            r'^(what|how|why|when|where|who|which)',  # Starts with question word
            r'(can you|could you|would you|tell me|describe|explain)',  # Common question phrases
        ]
        
        # Check patterns
        for pattern in question_patterns:
            if re.search(pattern, text_lower):
                return True
        
        # Check for question words
        if any(word in text_lower for word in question_words):
            return True
        
        # Avoid duplicate questions (within 5 seconds)
        current_time = time.time()
        if current_time - self.last_question_time < self.question_threshold:
            return False
        
        return len(text.split()) > 3  # Minimum word count

    def handle_detected_question(self, question_text, source='audio'):
        """Handle detected question and generate answer"""
        current_time = time.time()
        self.last_question_time = current_time
        
        print(f"\n🤖 Processing {source} question...")
        
        # Get screen context if available
        screen_context = ""
        if source != 'screen_only':
            screenshot = self.capture_screen()
            if screenshot:
                screen_context = self.analyze_screen_context(screenshot)
        
        # Generate AI response
        ai_answer = self.generate_intelligent_answer(question_text, screen_context)
        
        # Display answer
        self.display_stealth_answer(question_text, ai_answer, source)
        
        # Store in database
        self.store_stealth_session(question_text, ai_answer, screen_context, source)
        
        # Update question history
        self.question_history.append({
            'timestamp': current_time,
            'question': question_text,
            'answer': ai_answer,
            'source': source
        })

    def generate_intelligent_answer(self, question, screen_context=""):
        """Generate intelligent answer based on question and context"""
        # Check cache first
        question_key = question.lower().strip()
        if question_key in self.answer_cache:
            cached_answer = self.answer_cache[question_key]
            print("💾 Using cached answer")
            return cached_answer
        
        # Determine question type
        question_type = self.analyze_question_type(question)
        
        # Build enhanced prompt
        prompt = self.build_enhanced_prompt(question, question_type, screen_context)
        
        try:
            headers = {'Content-Type': 'application/json'}
            
            payload = {
                "contents": [{
                    "parts": [{"text": prompt}]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 1500,
                }
            }

            response = requests.post(self.gemini_url, headers=headers, data=json.dumps(payload))
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    answer = result['candidates'][0]['content']['parts'][0]['text']
                    
                    # Cache the answer
                    self.answer_cache[question_key] = answer
                    
                    return answer
                else:
                    return "Unable to generate response at this time."
            else:
                return f"API Error: {response.status_code}"
                
        except Exception as e:
            logger.error(f"Answer generation error: {e}")
            return "Error generating response. Please try again."

    def build_enhanced_prompt(self, question, question_type, screen_context):
        """Build enhanced prompt based on interview context"""
        
        company = self.interview_context.get('company', 'the company')
        role = self.interview_context.get('role', 'this position')
        
        base_prompt = f"""
You are an expert interview coach helping with a {role} interview at {company}.

Question: "{question}"
Question Type: {question_type}
Screen Context: {screen_context}

Provide a stellar interview answer that:
1. Directly answers the question
2. Shows enthusiasm for {company} and {role}
3. Demonstrates relevant skills and experience
4. Uses the STAR method when appropriate (Situation, Task, Action, Result)
5. Is conversational and confident
6. Includes specific examples when possible
7. Ends with a forward-looking statement

Keep the answer concise but comprehensive (2-3 minutes when spoken).
Structure it with clear talking points for easy delivery.

Answer:
"""

        # Add specific guidance based on question type
        if question_type == 'coding':
            base_prompt += "\nFocus on: Problem-solving approach, code walkthrough, complexity analysis, and alternative solutions."
        elif question_type == 'behavioral':
            base_prompt += "\nUse STAR method: Specific situation, your task, actions taken, and measurable results."
        elif question_type == 'technical':
            base_prompt += "\nExplain concepts clearly, show depth of knowledge, and relate to practical applications."
        elif question_type == 'company_specific':
            base_prompt += f"\nShow research about {company}, align with their values, and demonstrate genuine interest."

        return base_prompt

    def analyze_question_type(self, question):
        """Enhanced question type analysis"""
        question_lower = question.lower()
        
        # Define comprehensive patterns
        patterns = {
            'coding': ['code', 'algorithm', 'function', 'debug', 'programming', 'leetcode', 'data structure'],
            'behavioral': ['tell me about', 'describe a time', 'give an example', 'how do you handle', 'leadership', 'conflict'],
            'technical': ['how does', 'explain', 'what is', 'difference between', 'architecture', 'system design'],
            'company_specific': ['why', 'our company', 'this role', 'our team', 'culture', 'mission'],
            'experience': ['your experience', 'previous job', 'projects', 'worked on', 'background'],
            'situational': ['what would you do', 'how would you', 'if you were', 'scenario']
        }
        
        # Score each type
        scores = {}
        for q_type, keywords in patterns.items():
            score = sum(1 for keyword in keywords if keyword in question_lower)
            if score > 0:
                scores[q_type] = score
        
        # Return highest scoring type
        if scores:
            return max(scores, key=scores.get)
        else:
            return 'general'

    def display_stealth_answer(self, question, answer, source):
        """Display answer in stealth mode format"""
        print("\n" + "🕵️" * 50)
        print("💡 STEALTH ASSISTANT RESPONSE")
        print("🕵️" * 50)
        print(f"📝 Question ({source}): {question[:100]}...")
        print("─" * 50)
        print(f"🎯 AI Answer:")
        print(answer)
        print("🕵️" * 50)
        
        # Quick talking points extraction
        talking_points = self.extract_talking_points(answer)
        if talking_points:
            print("🎤 KEY TALKING POINTS:")
            for i, point in enumerate(talking_points, 1):
                print(f"  {i}. {point}")
            print("─" * 50)

    def extract_talking_points(self, answer):
        """Extract key talking points from answer"""
        # Split into sentences and find key ones
        sentences = [s.strip() for s in answer.split('.') if len(s.strip()) > 10]
        
        # Simple scoring based on key indicators
        talking_points = []
        for sentence in sentences[:5]:  # Top 5 sentences
            if any(indicator in sentence.lower() for indicator in 
                   ['first', 'second', 'third', 'additionally', 'furthermore', 'key', 'important', 'example']):
                talking_points.append(sentence[:80] + "..." if len(sentence) > 80 else sentence)
        
        return talking_points[:3]  # Top 3 points

    def continuous_screen_monitoring(self):
        """Continuous screen monitoring for questions"""
        print("🖥️ Starting continuous screen monitoring...")
        
        last_screenshot = None
        
        while self.screen_monitoring:
            try:
                current_time = time.time()
                
                # Check screen every 2 seconds
                if current_time - self.last_screen_check > self.screen_change_threshold:
                    screenshot = self.capture_screen()
                    
                    if screenshot and self.screen_has_changed(screenshot, last_screenshot):
                        screen_text = self.extract_screen_text(screenshot)
                        
                        if screen_text:
                            questions = self.find_questions_in_text(screen_text)
                            for question in questions:
                                if not self.is_duplicate_question(question):
                                    print(f"\n🖥️ SCREEN QUESTION DETECTED: {question}")
                                    self.handle_detected_question(question, 'screen')
                        
                        last_screenshot = screenshot
                    
                    self.last_screen_check = current_time
                
                time.sleep(0.5)  # Check every 500ms
                
            except Exception as e:
                logger.error(f"Screen monitoring error: {e}")
                time.sleep(1)

    def screen_has_changed(self, current_screenshot, last_screenshot):
        """Check if screen has changed significantly"""
        if last_screenshot is None:
            return True
        
        try:
            # Simple comparison based on image hash or size
            return current_screenshot.size != last_screenshot.size
        except:
            return True

    def extract_screen_text(self, screenshot):
        """Extract text from screenshot using OCR"""
        try:
            import pytesseract
            text = pytesseract.image_to_string(screenshot)
            return text
        except ImportError:
            print("⚠️ OCR not available. Install pytesseract for screen text extraction.")
            return ""
        except Exception as e:
            logger.error(f"OCR error: {e}")
            return ""

    def find_questions_in_text(self, text):
        """Find questions in extracted text"""
        questions = []
        
        # Split into lines and sentences
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if len(line) > 10 and self.is_likely_question(line):
                questions.append(line)
        
        return questions[:3]  # Maximum 3 questions at once

    def is_duplicate_question(self, question):
        """Check if question was recently processed"""
        question_lower = question.lower().strip()
        
        # Check recent history (last 5 questions)
        for recent_q in self.question_history[-5:]:
            if question_lower in recent_q['question'].lower() or recent_q['question'].lower() in question_lower:
                return True
        
        return False

    def capture_screen(self):
        """Enhanced screen capture"""
        try:
            screenshot = pyautogui.screenshot()
            return screenshot
        except Exception as e:
            logger.error(f"Screen capture error: {e}")
            return None

    def analyze_screen_context(self, screenshot):
        """Analyze screen for additional context"""
        if not screenshot:
            return ""
        
        try:
            # Convert to base64 for API
            image_base64 = self.image_to_base64(screenshot)
            if not image_base64:
                return ""
            
            # Quick context analysis
            context_prompt = {
                "contents": [{
                    "parts": [
                        {
                            "text": "Briefly describe what's visible on this screen. Focus on: interview platform, question type, coding environment, or document content. Keep it under 100 words."
                        },
                        {
                            "inline_data": {
                                "mime_type": "image/png",
                                "data": image_base64
                            }
                        }
                    ]
                }]
            }
            
            # Make quick API call
            headers = {'Content-Type': 'application/json'}
            payload = {
                **context_prompt,
                "generationConfig": {
                    "temperature": 0.3,
                    "maxOutputTokens": 200,
                }
            }
            
            response = requests.post(self.gemini_url, headers=headers, data=json.dumps(payload))
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    return result['candidates'][0]['content']['parts'][0]['text']
            
            return ""
            
        except Exception as e:
            logger.error(f"Screen context analysis error: {e}")
            return ""

    def image_to_base64(self, image):
        """Convert image to base64"""
        try:
            buffered = io.BytesIO()
            image.save(buffered, format="PNG")
            img_str = base64.b64encode(buffered.getvalue()).decode()
            return img_str
        except Exception as e:
            logger.error(f"Image conversion error: {e}")
            return None

    def store_stealth_session(self, question, answer, screen_context, source):
        """Store session data in database"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                INSERT INTO stealth_sessions 
                (timestamp, company, role, question_text, question_type, ai_answer, 
                 response_time, screen_context, confidence_score)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                datetime.now().isoformat(),
                self.interview_context.get('company', ''),
                self.interview_context.get('role', ''),
                question[:500],
                self.analyze_question_type(question),
                answer[:1000],
                time.time() - self.last_question_time,
                screen_context[:300],
                8.5  # Default confidence
            ))
            self.conn.commit()
        except Exception as e:
            logger.error(f"Database storage error: {e}")

    def setup_interview_context(self):
        """Setup interview context for better responses"""
        print("\n📋 INTERVIEW CONTEXT SETUP")
        print("=" * 40)
        
        self.interview_context['company'] = input("Company Name: ").strip()
        self.interview_context['role'] = input("Role/Position: ").strip()
        self.interview_context['interviewer_name'] = input("Interviewer Name (optional): ").strip()
        
        print("\nInterview Type:")
        print("1. Technical/Coding")
        print("2. Behavioral") 
        print("3. System Design")
        print("4. General/Mixed")
        
        choice = input("Select type (1-4): ").strip()
        type_map = {'1': 'technical', '2': 'behavioral', '3': 'system_design', '4': 'general'}
        self.interview_context['interview_type'] = type_map.get(choice, 'general')
        
        print("\n✅ Interview context configured!")
        print(f"🏢 Company: {self.interview_context['company']}")
        print(f"💼 Role: {self.interview_context['role']}")
        print(f"🎯 Type: {self.interview_context['interview_type']}")

    def start_full_stealth_mode(self):
        """Start full stealth mode with both audio and screen monitoring"""
        print("\n🚀 STARTING FULL STEALTH MODE...")
        print("🎧 Audio monitoring: ON")
        print("🖥️ Screen monitoring: ON")
        print("🤖 AI assistance: ACTIVE")
        print("\n⚠️ Use ethically and responsibly!")
        
        # Start audio monitoring thread
        if self.stereo_mix_enabled:
            audio_thread = threading.Thread(target=self.start_audio_monitoring, daemon=True)
            audio_thread.start()
            print("✅ Audio thread started")
        
        # Start screen monitoring thread
        self.screen_monitoring = True
        screen_thread = threading.Thread(target=self.continuous_screen_monitoring, daemon=True)
        screen_thread.start()
        print("✅ Screen thread started")
        
        # Start hotkey monitoring
        self.setup_hotkeys()
        print("✅ Hotkeys configured")
        
        # Main monitoring loop
        try:
            print("\n🕵️ STEALTH MODE ACTIVE - Monitoring interview...")
            print("Press Ctrl+C to stop")
            
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Stopping stealth mode...")
            self.is_recording = False
            self.screen_monitoring = False

    def setup_hotkeys(self):
        """Setup enhanced hotkeys with Caps Lock awareness"""
        try:
            # Enhanced manual screen analysis with Caps Lock detection
            keyboard.add_hotkey('caps lock', self.enhanced_manual_screen_analysis)

            # Emergency help (works regardless of Caps Lock state)
            keyboard.add_hotkey('f11', self.emergency_help)

            # Live insights (works regardless of Caps Lock state)
            keyboard.add_hotkey('f12', self.show_live_insights)

            # Quick question lookup (works regardless of Caps Lock state)
            keyboard.add_hotkey('ctrl+shift+q', self.quick_question_lookup)

            # Additional enhanced hotkeys
            keyboard.add_hotkey('ctrl+shift+s', self.toggle_audio_streaming)
            keyboard.add_hotkey('ctrl+shift+d', self.toggle_status_dashboard)
            keyboard.add_hotkey('ctrl+shift+r', self.restart_audio_system)

            # Caps Lock state monitoring
            self.start_caps_lock_monitoring()

            print("⌨️ Enhanced hotkeys configured successfully!")
            print("🔄 Caps Lock awareness enabled!")

        except Exception as e:
            print(f"⚠️ Hotkey setup error: {e}")

    def start_caps_lock_monitoring(self):
        """Start monitoring Caps Lock state changes"""
        try:
            caps_monitor_thread = threading.Thread(
                target=self._caps_lock_monitor_worker,
                daemon=True
            )
            caps_monitor_thread.start()
            self.threads.append(caps_monitor_thread)

        except Exception as e:
            logger.error(f"Caps Lock monitoring error: {e}")

    def _caps_lock_monitor_worker(self):
        """Monitor Caps Lock state changes"""
        last_state = self.system_monitor.get_caps_lock_state()

        while not self.shutdown_event.is_set():
            try:
                current_state = self.system_monitor.update_caps_lock_state()

                if current_state != last_state:
                    state_text = "ON" if current_state else "OFF"
                    print(f"\n🔄 Caps Lock: {state_text}")

                    # Trigger notification if enabled
                    if self.config.getboolean('UI', 'enable_notifications', True):
                        self._show_caps_lock_notification(current_state)

                    last_state = current_state

                time.sleep(0.5)  # Check twice per second

            except Exception as e:
                logger.error(f"Caps Lock monitor error: {e}")
                time.sleep(1)

    def _show_caps_lock_notification(self, caps_state):
        """Show Caps Lock state notification"""
        try:
            state_text = "ON" if caps_state else "OFF"
            # This could be enhanced with system notifications
            logger.info(f"Caps Lock state changed to: {state_text}")
        except Exception as e:
            logger.error(f"Notification error: {e}")

    def enhanced_manual_screen_analysis(self):
        """Enhanced manual screen analysis with Caps Lock awareness"""
        try:
            caps_state = self.system_monitor.get_caps_lock_state()
            print(f"\n🖱️ Manual screen analysis triggered (Caps Lock: {'ON' if caps_state else 'OFF'})")

            screenshot = self.capture_screen()
            if screenshot:
                screen_context = self.analyze_screen_context(screenshot)
                screen_text = self.extract_screen_text(screenshot)

                if screen_text:
                    questions = self.find_questions_in_text(screen_text)
                    for question in questions:
                        print(f"\n🖱️ MANUAL ANALYSIS: {question}")
                        self.handle_detected_question(question, 'manual')
                else:
                    print("🔍 Manual screen analysis - no questions detected")

                    # Show screen context anyway
                    if screen_context:
                        print(f"📄 Screen Context: {screen_context[:200]}...")
            else:
                print("❌ Failed to capture screen")

        except Exception as e:
            logger.error(f"Enhanced manual screen analysis error: {e}")

    def toggle_audio_streaming(self):
        """Toggle audio streaming on/off"""
        try:
            if self.audio_streamer.is_streaming:
                self.audio_streamer.stop_streaming()
                print("🔇 Audio streaming stopped")
            else:
                if self.stereo_mix_enabled:
                    self.audio_streamer.start_continuous_streaming(
                        self.stereo_mix_device_index,
                        self.sample_rate,
                        self.channels
                    )
                    print("🔊 Audio streaming started")
                else:
                    print("❌ Stereo Mix not available")
        except Exception as e:
            logger.error(f"Audio streaming toggle error: {e}")

    def toggle_status_dashboard(self):
        """Toggle status dashboard visibility"""
        try:
            if self.status_dashboard:
                try:
                    self.status_dashboard.withdraw()  # Hide
                    print("📊 Status dashboard hidden")
                except:
                    # Dashboard might be closed, recreate it
                    self.initialize_status_dashboard()
                    print("📊 Status dashboard shown")
            else:
                self.initialize_status_dashboard()
                print("📊 Status dashboard created")
        except Exception as e:
            logger.error(f"Dashboard toggle error: {e}")

    def restart_audio_system(self):
        """Restart the audio system"""
        try:
            print("🔄 Restarting audio system...")

            # Stop current audio
            self.audio_streamer.stop_streaming()
            self.audio_streamer.stop_playback()
            self.is_recording = False

            time.sleep(1)  # Brief pause

            # Reinitialize
            self.initialize_audio_system()

            print("✅ Audio system restarted!")

        except Exception as e:
            logger.error(f"Audio system restart error: {e}")

    def manual_screen_analysis(self):
        """Manual screen analysis trigger"""
        screenshot = self.capture_screen()
        if screenshot:
            screen_context = self.analyze_screen_context(screenshot)
            screen_text = self.extract_screen_text(screenshot)
            
            if screen_text:
                questions = self.find_questions_in_text(screen_text)
                for question in questions:
                    print(f"\n🖱️ MANUAL ANALYSIS: {question}")
                    self.handle_detected_question(question, 'manual')
            else:
                print("🔍 Manual screen analysis - no questions detected")

    def emergency_help(self):
        """Emergency help for difficult questions"""
        print("\n🚨 EMERGENCY HELP MODE")
        
        # Quick screen capture and analysis
        screenshot = self.capture_screen()
        if screenshot:
            image_base64 = self.image_to_base64(screenshot)
            if image_base64:
                emergency_prompt = {
                    "contents": [{
                        "parts": [
                            {
                                "text": "EMERGENCY: Quickly analyze this screen and provide the most important points to mention in an interview answer. Be extremely concise and actionable."
                            },
                            {
                                "inline_data": {
                                    "mime_type": "image/png",
                                    "data": image_base64
                                }
                            }
                        ]
                    }]
                }
                
                # Quick API call
                headers = {'Content-Type': 'application/json'}
                payload = {
                    **emergency_prompt,
                    "generationConfig": {
                        "temperature": 0.3,
                        "maxOutputTokens": 300,
                    }
                }
                
                try:
                    response = requests.post(self.gemini_url, headers=headers, data=json.dumps(payload))
                    if response.status_code == 200:
                        result = response.json()
                        if 'candidates' in result and len(result['candidates']) > 0:
                            emergency_help = result['candidates'][0]['content']['parts'][0]['text']
                            
                            print("\n🚨 EMERGENCY ASSISTANCE:")
                            print("=" * 40)
                            print(emergency_help)
                            print("=" * 40)
                
                except Exception as e:
                    print(f"❌ Emergency help error: {e}")

    def show_live_insights(self):
        """Show live performance insights"""
        print("\n📊 LIVE INSIGHTS DASHBOARD")
        print("=" * 50)
        
        # Recent questions analysis
        if self.question_history:
            recent_questions = self.question_history[-3:]
            print("🔄 Recent Questions:")
            for i, q in enumerate(recent_questions, 1):
                q_type = self.analyze_question_type(q['question'])
                print(f"  {i}. [{q_type.upper()}] {q['question'][:50]}...")
        
        # Performance metrics
        if len(self.question_history) > 0:
            avg_response_time = sum(time.time() - q['timestamp'] for q in self.question_history[-5:]) / min(5, len(self.question_history))
            print(f"\n⚡ Avg Response Time: {avg_response_time:.1f}s")
            
            # Question type distribution
            types = [self.analyze_question_type(q['question']) for q in self.question_history[-10:]]
            type_counts = {}
            for t in types:
                type_counts[t] = type_counts.get(t, 0) + 1
            
            print("📈 Question Types (Last 10):")
            for q_type, count in type_counts.items():
                print(f"  {q_type}: {count}")
        
        print("=" * 50)

    def quick_question_lookup(self):
        """Quick question lookup functionality"""
        print("\n🔍 QUICK QUESTION LOOKUP")
        
        # Get last detected question
        if self.question_history:
            last_question = self.question_history[-1]['question']
            print(f"Last Question: {last_question}")
            
            # Generate quick tips
            tips_prompt = f"""
            For this interview question: "{last_question}"
            
            Provide 3 quick tips:
            1. Key point to emphasize
            2. Example to mention
            3. Common mistake to avoid
            
            Be extremely brief and actionable.
            """
            
            try:
                headers = {'Content-Type': 'application/json'}
                payload = {
                    "contents": [{"parts": [{"text": tips_prompt}]}],
                    "generationConfig": {
                        "temperature": 0.5,
                        "maxOutputTokens": 200,
                    }
                }
                
                response = requests.post(self.gemini_url, headers=headers, data=json.dumps(payload))
                if response.status_code == 200:
                    result = response.json()
                    if 'candidates' in result and len(result['candidates']) > 0:
                        tips = result['candidates'][0]['content']['parts'][0]['text']
                        print("\n💡 QUICK TIPS:")
                        print(tips)
            
            except Exception as e:
                print(f"❌ Quick lookup error: {e}")
        else:
            print("No recent questions found")

    def audio_diagnostics(self):
        """Diagnose audio system issues"""
        print("\n🔧 AUDIO SYSTEM DIAGNOSTICS")
        print("=" * 40)
        
        # List all audio devices
        print("🎧 Available Audio Devices:")
        try:
            device_count = self.audio.get_device_count()
            
            for i in range(device_count):
                device_info = self.audio.get_device_info_by_index(i)
                device_type = "INPUT" if device_info['maxInputChannels'] > 0 else "OUTPUT"
                
                print(f"  {i}: {device_info['name']} [{device_type}]")
                print(f"      Channels: {device_info['maxInputChannels']} | Rate: {int(device_info['defaultSampleRate'])}Hz")
                
                # Check if it's stereo mix
                if 'stereo mix' in device_info['name'].lower():
                    print("      ✅ STEREO MIX DETECTED!")
        
        except Exception as e:
            print(f"❌ Device enumeration error: {e}")
        
        # Test stereo mix
        if self.stereo_mix_enabled:
            print(f"\n🎯 Current Stereo Mix Device: {self.stereo_mix_device_index}")
            print("🧪 Testing audio capture...")
            
            try:
                # Quick test recording
                test_stream = self.audio.open(
                    format=pyaudio.paInt16,
                    channels=self.channels,
                    rate=self.sample_rate,
                    input=True,
                    input_device_index=self.stereo_mix_device_index,
                    frames_per_buffer=1024
                )
                
                print("Recording 3 seconds of audio...")
                frames = []
                for _ in range(0, int(self.sample_rate / 1024 * 3)):
                    data = test_stream.read(1024)
                    frames.append(data)
                
                test_stream.stop_stream()
                test_stream.close()
                
                # Analyze volume
                audio_data = np.frombuffer(b''.join(frames), dtype=np.int16)
                volume = np.sqrt(np.mean(audio_data**2))
                
                print(f"✅ Audio test complete!")
                print(f"📊 Average Volume: {volume:.2f}")
                
                if volume > 100:
                    print("🔊 Good audio levels detected!")
                else:
                    print("⚠️ Low audio levels - check system volume")
                
            except Exception as e:
                print(f"❌ Audio test failed: {e}")
        else:
            print("\n❌ Stereo Mix not available")
            print("💡 Setup Instructions:")
            print("1. Right-click speaker icon in system tray")
            print("2. Select 'Open Sound settings'")
            print("3. Go to 'Sound Control Panel'")
            print("4. Recording tab > Right-click > Show Disabled Devices")
            print("5. Enable 'Stereo Mix' and set as default")

    def interviewer_behavior_analysis(self):
        """Analyze interviewer patterns"""
        print("\n🎭 INTERVIEWER BEHAVIOR ANALYSIS")
        print("=" * 50)
        
        if len(self.question_history) < 3:
            print("❌ Need at least 3 questions for analysis")
            return
        
        # Analyze question patterns
        recent_questions = [q['question'] for q in self.question_history[-10:]]
        
        analysis_prompt = f"""
        Analyze these interview questions to understand the interviewer's style:
        
        Questions: {recent_questions}
        
        Provide:
        1. Interview style (aggressive, conversational, technical-focused, etc.)
        2. Difficulty progression pattern
        3. Areas of focus (technical, behavioral, experience)
        4. Likely next question types
        5. Adaptation strategy for remaining interview
        
        Be concise and actionable.
        """
        
        try:
            headers = {'Content-Type': 'application/json'}
            payload = {
                "contents": [{"parts": [{"text": analysis_prompt}]}],
                "generationConfig": {
                    "temperature": 0.6,
                    "maxOutputTokens": 500,
                }
            }
            
            response = requests.post(self.gemini_url, headers=headers, data=json.dumps(payload))
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    analysis = result['candidates'][0]['content']['parts'][0]['text']
                    
                    print("🎯 INTERVIEWER ANALYSIS:")
                    print("=" * 40)
                    print(analysis)
                    print("=" * 40)
        
        except Exception as e:
            print(f"❌ Analysis error: {e}")

    def performance_dashboard(self):
        """Show comprehensive performance dashboard"""
        print("\n📈 PERFORMANCE DASHBOARD")
        print("=" * 60)
        
        try:
            cursor = self.conn.cursor()
            
            # Session statistics
            cursor.execute("""
                SELECT COUNT(*), AVG(confidence_score), AVG(response_time)
                FROM stealth_sessions 
                WHERE timestamp > datetime('now', '-1 day')
            """)
            
            daily_stats = cursor.fetchone()
            if daily_stats and daily_stats[0] > 0:
                print(f"📊 Today's Stats:")
                print(f"  Questions Handled: {daily_stats[0]}")
                print(f"  Avg Confidence: {daily_stats[1]:.1f}/10")
                print(f"  Avg Response Time: {daily_stats[2]:.1f}s")
            
            # Question type breakdown
            cursor.execute("""
                SELECT question_type, COUNT(*), AVG(confidence_score)
                FROM stealth_sessions 
                WHERE timestamp > datetime('now', '-7 days')
                GROUP BY question_type
            """)
            
            type_stats = cursor.fetchall()
            if type_stats:
                print(f"\n📋 Question Types (7 days):")
                for q_type, count, avg_conf in type_stats:
                    print(f"  {q_type.title()}: {count} questions (Avg: {avg_conf:.1f}/10)")
            
            # Performance trends
            print(f"\n📈 Performance Insights:")
            if len(self.question_history) > 5:
                recent_types = [self.analyze_question_type(q['question']) for q in self.question_history[-5:]]
                most_common = max(set(recent_types), key=recent_types.count)
                print(f"  Most Common Recent Type: {most_common}")
                
                # Response time trend
                recent_times = [time.time() - q['timestamp'] for q in self.question_history[-5:]]
                avg_recent_time = sum(recent_times) / len(recent_times)
                print(f"  Recent Avg Response Time: {avg_recent_time:.1f}s")
            
            print("=" * 60)
            
        except Exception as e:
            print(f"❌ Dashboard error: {e}")

    def run_stealth_assistant(self):
        """Enhanced main function to run the stealth assistant"""
        while not self.shutdown_event.is_set():
            try:
                print("\n🕵️ ENHANCED STEALTH ASSISTANT CONTROL PANEL")
                choice = input("\nEnter your choice (0-9, A-C): ").strip().upper()

                if choice == '1':
                    # Enhanced audio monitoring
                    if self.stereo_mix_enabled:
                        self.is_recording = True
                        audio_thread = threading.Thread(target=self.start_audio_monitoring, daemon=True)
                        audio_thread.start()
                        self.threads.append(audio_thread)
                        print("🎧 Enhanced audio monitoring started! Press Ctrl+C to stop.")
                        try:
                            while self.is_recording and not self.shutdown_event.is_set():
                                time.sleep(1)
                        except KeyboardInterrupt:
                            self.is_recording = False
                            print("\n🛑 Audio monitoring stopped")
                    else:
                        print("❌ Stereo Mix not available!")

                elif choice == '2':
                    # Advanced screen monitoring
                    self.screen_monitoring = True
                    screen_thread = threading.Thread(target=self.continuous_screen_monitoring, daemon=True)
                    screen_thread.start()
                    self.threads.append(screen_thread)
                    print("🖥️ Advanced screen monitoring started! Press Ctrl+C to stop.")
                    try:
                        while self.screen_monitoring and not self.shutdown_event.is_set():
                            time.sleep(1)
                    except KeyboardInterrupt:
                        self.screen_monitoring = False
                        print("\n🛑 Screen monitoring stopped")

                elif choice == '3':
                    # Full enhanced stealth mode
                    self.start_enhanced_stealth_mode()

                elif choice == '4':
                    # Interview context setup
                    self.setup_interview_context()

                elif choice == '5':
                    # Question pattern analysis
                    if self.question_history:
                        self.interviewer_behavior_analysis()
                    else:
                        print("❌ No questions recorded yet")

                elif choice == '6':
                    # Performance dashboard
                    self.performance_dashboard()

                elif choice == '7':
                    # Emergency assistance
                    self.emergency_help()

                elif choice == '8':
                    # Interviewer behavior analysis
                    self.interviewer_behavior_analysis()

                elif choice == '9':
                    # Audio diagnostics
                    self.audio_diagnostics()

                elif choice == 'A':
                    # Continuous audio streaming control
                    self.audio_streaming_control()

                elif choice == 'B':
                    # Status dashboard control
                    self.dashboard_control()

                elif choice == 'C':
                    # Configuration management
                    self.configuration_management()

                elif choice == '0':
                    # Start enhanced stealth assistant
                    self.start_enhanced_stealth_mode()

                else:
                    print("❌ Invalid choice! Please enter 0-9 or A-C.")

            except KeyboardInterrupt:
                print("\n👋 Exiting Enhanced Stealth Assistant...")
                self.shutdown_event.set()
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                logger.error(f"Main loop error: {e}")

        # Cleanup on exit
        self.cleanup_resources()

    def start_enhanced_stealth_mode(self):
        """Start enhanced full stealth mode with all new features"""
        print("\n🚀 STARTING ENHANCED STEALTH MODE...")
        print("🎧 Enhanced audio monitoring: ON")
        print("🖥️ Advanced screen monitoring: ON")
        print("🤖 AI assistance: ACTIVE")
        print("📊 Status dashboard: ENABLED")
        print("🔄 Continuous streaming: ACTIVE")
        print("🔒 Caps Lock awareness: ON")
        print("\n⚠️ Use ethically and responsibly!")

        try:
            # Start enhanced audio monitoring
            if self.stereo_mix_enabled:
                self.is_recording = True
                audio_thread = threading.Thread(target=self.start_audio_monitoring, daemon=True)
                audio_thread.start()
                self.threads.append(audio_thread)
                print("✅ Enhanced audio thread started")

            # Start advanced screen monitoring
            self.screen_monitoring = True
            screen_thread = threading.Thread(target=self.continuous_screen_monitoring, daemon=True)
            screen_thread.start()
            self.threads.append(screen_thread)
            print("✅ Advanced screen thread started")

            # Start enhanced hotkey monitoring
            self.setup_hotkeys()
            print("✅ Enhanced hotkeys configured")

            # Show status dashboard if not already shown
            if not self.status_dashboard and self.config.getboolean('UI', 'show_status_dashboard', True):
                self.initialize_status_dashboard()
                print("✅ Status dashboard initialized")

            # Main enhanced monitoring loop
            print("\n🕵️ ENHANCED STEALTH MODE ACTIVE - Monitoring interview...")
            print("📊 Check status dashboard for real-time information")
            print("Press Ctrl+C to stop")

            while not self.shutdown_event.is_set():
                time.sleep(1)

        except KeyboardInterrupt:
            print("\n🛑 Stopping enhanced stealth mode...")
            self.shutdown_event.set()
        except Exception as e:
            logger.error(f"Enhanced stealth mode error: {e}")
            print(f"❌ Enhanced stealth mode error: {e}")
        finally:
            self.is_recording = False
            self.screen_monitoring = False

    def audio_streaming_control(self):
        """Control continuous audio streaming"""
        print("\n🔄 AUDIO STREAMING CONTROL")
        print("=" * 40)
        print("1. Start continuous streaming")
        print("2. Stop continuous streaming")
        print("3. Restart streaming")
        print("4. Show streaming status")
        print("5. Configure streaming settings")
        print("0. Back to main menu")

        choice = input("\nEnter choice: ").strip()

        if choice == '1':
            if not self.audio_streamer.is_streaming and self.stereo_mix_enabled:
                self.audio_streamer.start_continuous_streaming(
                    self.stereo_mix_device_index,
                    self.sample_rate,
                    self.channels
                )
                self.audio_streamer.start_continuous_playback()
                print("✅ Continuous streaming started!")
            else:
                print("⚠️ Streaming already active or Stereo Mix not available")

        elif choice == '2':
            self.audio_streamer.stop_streaming()
            self.audio_streamer.stop_playback()
            print("🛑 Continuous streaming stopped!")

        elif choice == '3':
            self.restart_audio_system()

        elif choice == '4':
            streaming_status = "ACTIVE" if self.audio_streamer.is_streaming else "INACTIVE"
            playback_status = "ACTIVE" if self.audio_streamer.is_playing else "INACTIVE"
            print(f"📊 Streaming Status: {streaming_status}")
            print(f"📊 Playback Status: {playback_status}")
            print(f"📊 Queue Size: {self.audio_streamer.audio_queue.qsize()}")

        elif choice == '5':
            self.configure_streaming_settings()

    def dashboard_control(self):
        """Control status dashboard"""
        print("\n📱 STATUS DASHBOARD CONTROL")
        print("=" * 40)
        print("1. Show dashboard")
        print("2. Hide dashboard")
        print("3. Restart dashboard")
        print("4. Configure dashboard")
        print("0. Back to main menu")

        choice = input("\nEnter choice: ").strip()

        if choice == '1':
            if not self.status_dashboard:
                self.initialize_status_dashboard()
                print("✅ Dashboard shown!")
            else:
                try:
                    self.status_dashboard.deiconify()  # Show if hidden
                    print("✅ Dashboard restored!")
                except:
                    self.initialize_status_dashboard()
                    print("✅ Dashboard recreated!")

        elif choice == '2':
            if self.status_dashboard:
                try:
                    self.status_dashboard.withdraw()  # Hide
                    print("📊 Dashboard hidden!")
                except:
                    print("⚠️ Dashboard not available")
            else:
                print("⚠️ Dashboard not active")

        elif choice == '3':
            if self.status_dashboard:
                try:
                    self.status_dashboard.destroy()
                except:
                    pass
            self.initialize_status_dashboard()
            print("🔄 Dashboard restarted!")

        elif choice == '4':
            self.configure_dashboard_settings()

    def configuration_management(self):
        """Manage configuration settings"""
        print("\n⚙️ CONFIGURATION MANAGEMENT")
        print("=" * 40)
        print("1. View current configuration")
        print("2. Edit audio settings")
        print("3. Edit system settings")
        print("4. Edit AI settings")
        print("5. Edit UI settings")
        print("6. Reset to defaults")
        print("7. Save configuration")
        print("0. Back to main menu")

        choice = input("\nEnter choice: ").strip()

        if choice == '1':
            self.show_current_configuration()
        elif choice == '2':
            self.edit_audio_settings()
        elif choice == '3':
            self.edit_system_settings()
        elif choice == '4':
            self.edit_ai_settings()
        elif choice == '5':
            self.edit_ui_settings()
        elif choice == '6':
            self.config.create_default_config()
            print("✅ Configuration reset to defaults!")
        elif choice == '7':
            self.config.save_config()
            print("✅ Configuration saved!")

    def show_current_configuration(self):
        """Show current configuration"""
        print("\n📋 CURRENT CONFIGURATION")
        print("=" * 50)

        sections = ['AUDIO', 'SYSTEM', 'AI', 'UI']
        for section in sections:
            print(f"\n[{section}]")
            if self.config.config.has_section(section):
                for key, value in self.config.config[section].items():
                    print(f"  {key} = {value}")

    def configure_streaming_settings(self):
        """Configure streaming settings"""
        print("\n🔧 STREAMING SETTINGS")
        print("=" * 30)

        current_rate = self.config.get('AUDIO', 'sample_rate', '44100')
        new_rate = input(f"Sample Rate (current: {current_rate}): ").strip()
        if new_rate:
            self.config.config.set('AUDIO', 'sample_rate', new_rate)

        current_channels = self.config.get('AUDIO', 'channels', '2')
        new_channels = input(f"Channels (current: {current_channels}): ").strip()
        if new_channels:
            self.config.config.set('AUDIO', 'channels', new_channels)

        current_chunk = self.config.get('AUDIO', 'chunk_size', '1024')
        new_chunk = input(f"Chunk Size (current: {current_chunk}): ").strip()
        if new_chunk:
            self.config.config.set('AUDIO', 'chunk_size', new_chunk)

        self.config.save_config()
        print("✅ Streaming settings updated!")

    def configure_dashboard_settings(self):
        """Configure dashboard settings"""
        print("\n🎛️ DASHBOARD SETTINGS")
        print("=" * 30)

        current_viz = self.config.get('UI', 'show_audio_visualization', 'true')
        new_viz = input(f"Show Audio Visualization (current: {current_viz}) [true/false]: ").strip()
        if new_viz.lower() in ['true', 'false']:
            self.config.config.set('UI', 'show_audio_visualization', new_viz.lower())

        current_notif = self.config.get('UI', 'enable_notifications', 'true')
        new_notif = input(f"Enable Notifications (current: {current_notif}) [true/false]: ").strip()
        if new_notif.lower() in ['true', 'false']:
            self.config.config.set('UI', 'enable_notifications', new_notif.lower())

        self.config.save_config()
        print("✅ Dashboard settings updated!")

    def edit_audio_settings(self):
        """Edit audio configuration"""
        print("\n🎵 AUDIO SETTINGS")
        self.configure_streaming_settings()

    def edit_system_settings(self):
        """Edit system configuration"""
        print("\n⚙️ SYSTEM SETTINGS")
        print("=" * 30)

        current_threshold = self.config.get('SYSTEM', 'question_threshold', '5.0')
        new_threshold = input(f"Question Threshold (current: {current_threshold}): ").strip()
        if new_threshold:
            self.config.config.set('SYSTEM', 'question_threshold', new_threshold)

        current_buffer = self.config.get('SYSTEM', 'max_audio_buffer', '300')
        new_buffer = input(f"Max Audio Buffer (current: {current_buffer}): ").strip()
        if new_buffer:
            self.config.config.set('SYSTEM', 'max_audio_buffer', new_buffer)

        self.config.save_config()
        print("✅ System settings updated!")

    def edit_ai_settings(self):
        """Edit AI configuration"""
        print("\n🤖 AI SETTINGS")
        print("=" * 30)

        current_temp = self.config.get('AI', 'temperature', '0.7')
        new_temp = input(f"Temperature (current: {current_temp}): ").strip()
        if new_temp:
            self.config.config.set('AI', 'temperature', new_temp)

        current_tokens = self.config.get('AI', 'max_tokens', '1500')
        new_tokens = input(f"Max Tokens (current: {current_tokens}): ").strip()
        if new_tokens:
            self.config.config.set('AI', 'max_tokens', new_tokens)

        self.config.save_config()
        print("✅ AI settings updated!")

    def edit_ui_settings(self):
        """Edit UI configuration"""
        print("\n🎨 UI SETTINGS")
        self.configure_dashboard_settings()

def main():
    """Enhanced main function"""
    print("🕵️" * 25)
    print("🚀 ENHANCED STEALTH INTERVIEW ASSISTANT v2.0")
    print("🕵️" * 25)
    print("✨ ENHANCED FEATURES:")
    print("✅ Continuous Audio Streaming & Playback")
    print("✅ Caps Lock State Detection & Handling")
    print("✅ Real-time Status Dashboard with Visualization")
    print("✅ Advanced Voice Activity Detection")
    print("✅ Enhanced Caching & Performance Optimization")
    print("✅ Comprehensive Configuration Management")
    print("✅ System Audio Capture (Stereo Mix)")
    print("✅ Real-time Question Detection")
    print("✅ Advanced Screen Monitoring with OCR")
    print("✅ AI-Powered Smart Answers")
    print("✅ Interview Performance Analytics")
    print("✅ Enhanced Stealth Mode Operation")
    print("✅ Thread Management & Graceful Shutdown")
    print("🕵️" * 25)

    # Get API key
    api_key = input("\n🔑 Enter your Gemini API key: ").strip()

    if not api_key:
        print("❌ API key required!")
        return

    try:
        # Initialize enhanced assistant
        print("🚀 Initializing Enhanced Stealth Assistant...")
        assistant = StealthInterviewAssistant(api_key)

        # Run enhanced main loop
        assistant.run_stealth_assistant()

    except KeyboardInterrupt:
        print("\n👋 Graceful shutdown initiated...")
    except Exception as e:
        print(f"❌ Initialization error: {e}")
        print("\n💡 Common issues:")
        print("- Ensure Stereo Mix is enabled in Windows Sound settings")
        print("- Install required packages:")
        print("  pip install pyaudio whisper numpy matplotlib tkinter")
        print("  pip install pyautogui keyboard pillow requests")
        print("  pip install pydub webrtcvad psutil")
        print("- Check your Gemini API key")
        print("- Ensure you have proper permissions for audio capture")
        print("- Try running as administrator if audio issues persist")

if __name__ == "__main__":
    main()