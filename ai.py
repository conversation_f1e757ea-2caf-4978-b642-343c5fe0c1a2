#!/usr/bin/env python3
"""
Real-time Interview Assistant
Captures system audio directly from output devices, detects questions, and provides instant AI responses
Works without Stereo Mix - automatically detects default output and captures system audio
"""

import pyaudio
import wave
import threading
import time
import queue
import re
import os
import tempfile
import subprocess
import platform
from datetime import datetime
from typing import Optional, List, Dict
import numpy as np

# Required imports (install via pip)
try:
    import whisper
    import google.generativeai as genai
    import sounddevice as sd
    import soundfile as sf
except ImportError as e:
    print(f"Missing required packages. Install with:")
    print("pip install openai-whisper google-generativeai numpy pyaudio sounddevice soundfile")
    exit(1)

class AudioConfig:
    """Audio configuration constants"""
    RATE = 44100  # Standard rate for system audio
    CHUNK = 1024
    CHANNELS = 2  # Stereo for system audio
    FORMAT = pyaudio.paInt16
    RECORD_SECONDS = 2.5
    SILENCE_THRESHOLD = 500  # Lower threshold for system audio
    MIN_AUDIO_LENGTH = 0.5

class SystemAudioCapture:
    """Handles system audio capture using multiple methods"""
    
    def __init__(self):
        self.method = None
        self.device_id = None
        self.audio = None
        self.initialize()
    
    def initialize(self):
        """Initialize the best available audio capture method"""
        print("🎵 Initializing system audio capture...")
        
        # Method 1: Try to find WASAPI loopback (Windows)
        if platform.system() == "Windows":
            if self.try_wasapi_loopback():
                self.method = "wasapi"
                print("✅ Using WASAPI loopback (system audio)")
                return
        
        # Method 2: Try sounddevice with loopback
        if self.try_sounddevice_loopback():
            self.method = "sounddevice"
            print("✅ Using sounddevice loopback")
            return
        
        # Method 3: Try to find any system audio device
        if self.try_system_audio_device():
            self.method = "system_device"
            print("✅ Using system audio device")
            return
        
        # Method 4: Fallback to default microphone
        if self.try_default_microphone():
            self.method = "microphone"
            print("⚠️  Using default microphone (interviews must be played through speakers)")
            return
        
        raise Exception("No audio capture method available")
    
    def try_wasapi_loopback(self) -> bool:
        """Try to use WASAPI loopback for system audio capture"""
        try:
            import sounddevice as sd
            
            # Get all devices
            devices = sd.query_devices()
            
            # Look for WASAPI loopback devices
            for i, device in enumerate(devices):
                if device['max_input_channels'] > 0:
                    name = device['name'].lower()
                    if 'wasapi' in name or 'loopback' in name or 'speakers' in name:
                        try:
                            # Test the device
                            sd.check_input_settings(i, channels=2, samplerate=44100)
                            self.device_id = i
                            return True
                        except:
                            continue
            
            return False
        except:
            return False
    
    def try_sounddevice_loopback(self) -> bool:
        """Try sounddevice with system audio detection"""
        try:
            import sounddevice as sd
            
            # Try to detect default output device and use it as input
            default_output = sd.query_devices(kind='output')
            devices = sd.query_devices()
            
            # Look for devices that might capture system audio
            for i, device in enumerate(devices):
                if device['max_input_channels'] > 0:
                    name = device['name'].lower()
                    if any(keyword in name for keyword in ['mix', 'monitor', 'what u hear', 'wave out']):
                        try:
                            sd.check_input_settings(i, channels=1, samplerate=44100)
                            self.device_id = i
                            return True
                        except:
                            continue
            
            return False
        except:
            return False
    
    def try_system_audio_device(self) -> bool:
        """Try to find system audio devices using PyAudio"""
        try:
            self.audio = pyaudio.PyAudio()
            
            # Look for system audio devices
            for i in range(self.audio.get_device_count()):
                try:
                    info = self.audio.get_device_info_by_index(i)
                    if info['maxInputChannels'] > 0:
                        name = info['name'].lower()
                        
                        # Check for system audio keywords
                        if any(keyword in name for keyword in [
                            'stereo mix', 'what u hear', 'wave out mix', 
                            'speakers', 'headphones', 'monitor', 'loopback'
                        ]):
                            if self.test_pyaudio_device(i):
                                self.device_id = i
                                self.method = "pyaudio"
                                return True
                except:
                    continue
            
            return False
        except:
            return False
    
    def try_default_microphone(self) -> bool:
        """Fallback to default microphone"""
        try:
            if not self.audio:
                self.audio = pyaudio.PyAudio()
            
            # Try default input device
            try:
                default_device = self.audio.get_default_input_device_info()
                if self.test_pyaudio_device(default_device['index']):
                    self.device_id = default_device['index']
                    self.method = "pyaudio"
                    AudioConfig.CHANNELS = 1  # Microphone is usually mono
                    return True
            except:
                pass
            
            # Try first available input device
            for i in range(self.audio.get_device_count()):
                try:
                    info = self.audio.get_device_info_by_index(i)
                    if info['maxInputChannels'] > 0:
                        if self.test_pyaudio_device(i):
                            self.device_id = i
                            self.method = "pyaudio"
                            AudioConfig.CHANNELS = 1
                            return True
                except:
                    continue
            
            return False
        except:
            return False
    
    def test_pyaudio_device(self, device_id: int) -> bool:
        """Test if a PyAudio device works"""
        try:
            stream = self.audio.open(
                format=AudioConfig.FORMAT,
                channels=min(2, self.audio.get_device_info_by_index(device_id)['maxInputChannels']),
                rate=AudioConfig.RATE,
                input=True,
                input_device_index=device_id,
                frames_per_buffer=AudioConfig.CHUNK
            )
            
            # Try to read a small chunk
            stream.read(AudioConfig.CHUNK, exception_on_overflow=False)
            stream.stop_stream()
            stream.close()
            return True
        except:
            return False
    
    def capture_audio(self, duration: float = 2.5) -> Optional[np.ndarray]:
        """Capture audio using the selected method"""
        try:
            if self.method == "sounddevice":
                return self.capture_with_sounddevice(duration)
            elif self.method in ["wasapi", "system_device", "microphone", "pyaudio"]:
                return self.capture_with_pyaudio(duration)
            else:
                return None
        except Exception as e:
            print(f"Audio capture error: {e}")
            return None
    
    def capture_with_sounddevice(self, duration: float) -> Optional[np.ndarray]:
        """Capture audio using sounddevice"""
        try:
            import sounddevice as sd
            
            audio_data = sd.rec(
                int(duration * AudioConfig.RATE),
                samplerate=AudioConfig.RATE,
                channels=AudioConfig.CHANNELS,
                device=self.device_id
            )
            sd.wait()
            return audio_data.flatten()
        except:
            return None
    
    def capture_with_pyaudio(self, duration: float) -> Optional[np.ndarray]:
        """Capture audio using PyAudio"""
        try:
            if not self.audio:
                self.audio = pyaudio.PyAudio()
            
            stream = self.audio.open(
                format=AudioConfig.FORMAT,
                channels=AudioConfig.CHANNELS,
                rate=AudioConfig.RATE,
                input=True,
                input_device_index=self.device_id,
                frames_per_buffer=AudioConfig.CHUNK
            )
            
            frames = []
            for _ in range(int(AudioConfig.RATE / AudioConfig.CHUNK * duration)):
                try:
                    data = stream.read(AudioConfig.CHUNK, exception_on_overflow=False)
                    frames.append(data)
                except:
                    break
            
            stream.stop_stream()
            stream.close()
            
            if frames:
                audio_data = b''.join(frames)
                audio_np = np.frombuffer(audio_data, dtype=np.int16)
                
                # Convert stereo to mono if needed
                if AudioConfig.CHANNELS == 2 and len(audio_np) > 0:
                    audio_np = audio_np.reshape(-1, 2).mean(axis=1)
                
                return audio_np.astype(np.int16)
            
            return None
        except Exception as e:
            print(f"PyAudio capture error: {e}")
            return None
    
    def cleanup(self):
        """Clean up audio resources"""
        if self.audio:
            self.audio.terminate()

class InterviewAssistant:
    def __init__(self, gemini_api_key: str):
        """Initialize the interview assistant"""
        self.gemini_api_key = gemini_api_key
        self.setup_ai()
        self.setup_audio()
        self.setup_whisper()
        
        # Threading and queues
        self.audio_queue = queue.Queue()
        self.text_queue = queue.Queue()
        self.running = False
        
        # Question detection and context
        self.recent_questions = []
        self.last_processed_text = ""
        self.question_indicators = [
            '?', 'what', 'how', 'why', 'when', 'where', 'who', 'which',
            'tell me', 'explain', 'describe', 'can you', 'could you',
            'would you', 'do you', 'have you', 'are you', 'will you'
        ]
        
        # Error handling and device management
        self.device_failures = 0
        self.max_device_failures = 3
        self.available_devices = []
        
        # Status tracking
        self.status = "Initializing"
        self.last_audio_time = time.time()
        
    def setup_ai(self):
        """Setup Gemini AI"""
        try:
            genai.configure(api_key=self.gemini_api_key)
            self.model = genai.GenerativeModel('gemini-pro')
            self.ai_prompt = """You are an AI assistant helping with interview responses. 
            Provide concise, professional, and relevant answers to interview questions. 
            Focus on being helpful, confident, and articulate. Keep responses under 200 words 
            unless the question specifically requires detail. Be conversational but professional."""
            print("✓ Gemini AI initialized successfully")
        except Exception as e:
            print(f"✗ Failed to initialize Gemini AI: {e}")
            exit(1)
    
    def setup_whisper(self):
        """Setup Whisper model"""
        try:
            print("Loading Whisper model (this may take a moment)...")
            self.whisper_model = whisper.load_model("base")
            print("✓ Whisper model loaded successfully")
        except Exception as e:
            print(f"✗ Failed to load Whisper model: {e}")
            exit(1)
    
    def get_device_name(self, device_id: int) -> str:
        """Get device name by ID"""
        try:
            return self.audio.get_device_info_by_index(device_id)['name']
        except:
            return f"Device {device_id}"
    
    def list_audio_devices(self):
        """List all available audio devices"""
        print("\nAvailable audio devices:")
        for i in range(self.audio.get_device_count()):
            try:
                info = self.audio.get_device_info_by_index(i)
                if info['maxInputChannels'] > 0:
                    print(f"  {i}: {info['name']} (Input channels: {info['maxInputChannels']})")
            except:
                continue
    
    def is_silence(self, audio_data: bytes) -> bool:
        """Check if audio data contains mostly silence"""
        try:
            audio_np = np.frombuffer(audio_data, dtype=np.int16)
            volume = np.sqrt(np.mean(audio_np**2))
            return volume < AudioConfig.SILENCE_THRESHOLD
        except:
            return True
    
    def find_audio_device(self) -> Optional[int]:
        """Find the best available audio input device"""
        try:
            # First try to find system audio devices
            for i in range(self.audio.get_device_count()):
                try:
                    info = self.audio.get_device_info_by_index(i)
                    if info['maxInputChannels'] > 0:
                        name = info['name'].lower()
                        
                        # Check for system audio keywords
                        if any(keyword in name for keyword in [
                            'stereo mix', 'what u hear', 'wave out mix', 
                            'speakers', 'headphones', 'monitor', 'loopback'
                        ]):
                            if self.test_audio_device(i):
                                return i
                except:
                    continue
            
            # If no system audio device found, use default input
            try:
                default_device = self.audio.get_default_input_device_info()
                if self.test_audio_device(default_device['index']):
                    AudioConfig.CHANNELS = 1  # Microphone is usually mono
                    return default_device['index']
            except:
                pass
            
            # Last resort: find any working input device
            for i in range(self.audio.get_device_count()):
                try:
                    info = self.audio.get_device_info_by_index(i)
                    if info['maxInputChannels'] > 0:
                        if self.test_audio_device(i):
                            AudioConfig.CHANNELS = 1
                            return i
                except:
                    continue
            
            return None
        except Exception as e:
            print(f"Error finding audio device: {e}")
            return None
    
    def test_audio_device(self, device_id: int) -> bool:
        """Test if an audio device works"""
        try:
            stream = self.audio.open(
                format=AudioConfig.FORMAT,
                channels=min(2, self.audio.get_device_info_by_index(device_id)['maxInputChannels']),
                rate=AudioConfig.RATE,
                input=True,
                input_device_index=device_id,
                frames_per_buffer=AudioConfig.CHUNK
            )
            
            # Try to read a small chunk
            stream.read(AudioConfig.CHUNK, exception_on_overflow=False)
            stream.stop_stream()
            stream.close()
            return True
        except:
            return False
    
    def try_fallback_device(self):
        """Try to switch to a different working audio device"""
        self.device_failures += 1
        
        if self.device_failures >= self.max_device_failures:
            print(f"⚠️  Current device failed {self.device_failures} times, trying alternatives...")
            
            # Get all available devices
            if not self.available_devices:
                for i in range(self.audio.get_device_count()):
                    try:
                        info = self.audio.get_device_info_by_index(i)
                        if info['maxInputChannels'] > 0 and i != self.audio_device_id:
                            self.available_devices.append(i)
                    except:
                        continue
            
            # Try next device
            if self.available_devices:
                new_device = self.available_devices.pop(0)
                if self.test_audio_device(new_device):
                    print(f"✅ Switched to device: {self.get_device_name(new_device)}")
                    self.audio_device_id = new_device
                    self.device_failures = 0
                    return True
            
            print("❌ No more working devices available")
            return False
        
        return True

    def setup_audio(self):
        """Setup audio recording"""
        print("🎵 Initializing audio system...")
        self.audio = pyaudio.PyAudio()
        self.audio_device_id = self.find_audio_device()
        
        if self.audio_device_id is None:
            print("✗ No suitable audio device found")
            self.list_audio_devices()
            print("\n💡 TROUBLESHOOTING TIPS:")
            print("1. Enable 'Stereo Mix' in Windows Sound settings")
            print("2. Try running as Administrator")
            print("3. Check if another program is using the microphone")
            print("4. Try unplugging/reconnecting audio devices")
            exit(1)
        
        print(f"✓ Using audio device: {self.get_device_name(self.audio_device_id)}")
        print(f"✓ Audio format: {AudioConfig.RATE}Hz, {AudioConfig.CHANNELS} channel(s)")

    def record_audio_chunk(self) -> Optional[bytes]:
        """Record a chunk of audio"""
        try:
            stream = self.audio.open(
                format=AudioConfig.FORMAT,
                channels=AudioConfig.CHANNELS,
                rate=AudioConfig.RATE,
                input=True,
                input_device_index=self.audio_device_id,
                frames_per_buffer=AudioConfig.CHUNK
            )
            
            frames = []
            for _ in range(int(AudioConfig.RATE / AudioConfig.CHUNK * AudioConfig.RECORD_SECONDS)):
                try:
                    data = stream.read(AudioConfig.CHUNK, exception_on_overflow=False)
                    frames.append(data)
                except Exception as e:
                    print(f"Frame read error: {e}")
                    break
            
            stream.stop_stream()
            stream.close()
            
            if frames:
                audio_data = b''.join(frames)
                return audio_data if not self.is_silence(audio_data) else None
            
            return None
        except Exception as e:
            print(f"Audio recording error: {e}")
            return None

    def audio_capture_thread(self):
        """Continuous audio capture thread"""
        consecutive_failures = 0
        max_consecutive_failures = 5
        
        while self.running:
            try:
                self.status = "Listening..."
                audio_data = self.record_audio_chunk()
                
                if audio_data:
                    self.last_audio_time = time.time()
                    self.audio_queue.put(audio_data)
                    consecutive_failures = 0  # Reset failure counter
                else:
                    consecutive_failures += 1
                
                # If too many consecutive failures, try to recover
                if consecutive_failures >= max_consecutive_failures:
                    print(f"⚠️  {consecutive_failures} consecutive audio failures, attempting recovery...")
                    if not self.try_fallback_device():
                        print("❌ Audio recovery failed, continuing with current device...")
                    consecutive_failures = 0
                
                time.sleep(0.1)  # Small delay to prevent CPU overload
                
            except Exception as e:
                print(f"Audio capture thread error: {e}")
                consecutive_failures += 1
                time.sleep(1)
    
    def transcribe_audio(self, audio_data: bytes) -> Optional[str]:
        """Transcribe audio using Whisper"""
        try:
            # Save audio to temporary file
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                # Create WAV file
                with wave.open(temp_file.name, 'wb') as wav_file:
                    wav_file.setnchannels(AudioConfig.CHANNELS)
                    wav_file.setsampwidth(self.audio.get_sample_size(AudioConfig.FORMAT))
                    wav_file.setframerate(AudioConfig.RATE)
                    wav_file.writeframes(audio_data)
                
                # Transcribe with Whisper
                result = self.whisper_model.transcribe(temp_file.name)
                text = result['text'].strip()
                
                # Clean up
                os.unlink(temp_file.name)
                
                return text if len(text) > 3 else None
                
        except Exception as e:
            print(f"Transcription error: {e}")
            return None
    
    def is_question(self, text: str) -> bool:
        """Check if text contains a question"""
        text_lower = text.lower()
        
        # Check for question mark
        if '?' in text:
            return True
        
        # Check for question indicators
        for indicator in self.question_indicators:
            if indicator in text_lower:
                return True
        
        # Check if it's significantly different from last processed text
        if self.last_processed_text and text in self.last_processed_text:
            return False
        
        return False
    
    def is_duplicate_question(self, text: str) -> bool:
        """Check if this question was recently processed"""
        for recent_q in self.recent_questions[-3:]:  # Check last 3 questions
            if self.text_similarity(text, recent_q) > 0.8:
                return True
        return False
    
    def text_similarity(self, text1: str, text2: str) -> float:
        """Calculate simple text similarity"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def get_ai_response(self, question: str) -> str:
        """Get AI response from Gemini"""
        try:
            prompt = f"{self.ai_prompt}\n\nQuestion: {question}\n\nResponse:"
            response = self.model.generate_content(prompt)
            return response.text.strip()
            
        except Exception as e:
            return f"AI Error: {e}"
    
    def speech_processing_thread(self):
        """Process speech recognition and AI responses"""
        while self.running:
            try:
                if not self.audio_queue.empty():
                    self.status = "Processing audio..."
                    audio_data = self.audio_queue.get()
                    
                    # Transcribe audio
                    text = self.transcribe_audio(audio_data)
                    
                    if text and self.is_question(text):
                        if not self.is_duplicate_question(text):
                            self.status = "Generating response..."
                            
                            # Print detected question
                            timestamp = datetime.now().strftime("%H:%M:%S")
                            print(f"\n[{timestamp}] 🎤 QUESTION DETECTED:")
                            print(f"   \"{text}\"")
                            print("   " + "="*50)
                            
                            # Get AI response
                            response = self.get_ai_response(text)
                            
                            # Print AI response
                            print(f"[{timestamp}] 🤖 AI RESPONSE:")
                            print(f"   {response}")
                            print("   " + "-"*50)
                            
                            # Update tracking
                            self.recent_questions.append(text)
                            if len(self.recent_questions) > 5:
                                self.recent_questions.pop(0)
                            
                            self.last_processed_text = text
                
                time.sleep(0.1)
                
            except Exception as e:
                print(f"Processing error: {e}")
                time.sleep(1)
    
    def status_thread(self):
        """Display status updates"""
        while self.running:
            try:
                # Check if we're receiving audio
                time_since_audio = time.time() - self.last_audio_time
                if time_since_audio > 10:
                    status_msg = "⚠️  No audio detected (check audio device)"
                else:
                    status_msg = f"🟢 {self.status}"
                
                # Update status line
                print(f"\r{status_msg}", end="", flush=True)
                
                time.sleep(2)
                
            except Exception as e:
                print(f"Status error: {e}")
                time.sleep(5)
    
    def start(self):
        """Start the interview assistant"""
        print("\n" + "="*60)
        print("🎯 REAL-TIME INTERVIEW ASSISTANT STARTED")
        print("="*60)
        print("📝 Listening for interview questions...")
        print("⏹️  Press Ctrl+C to stop\n")
        
        self.running = True
        self.last_audio_time = time.time()
        
        # Start threads
        audio_thread = threading.Thread(target=self.audio_capture_thread, daemon=True)
        speech_thread = threading.Thread(target=self.speech_processing_thread, daemon=True)
        status_thread = threading.Thread(target=self.status_thread, daemon=True)
        
        audio_thread.start()
        speech_thread.start()
        status_thread.start()
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n🛑 Stopping interview assistant...")
            self.stop()
    
    def stop(self):
        """Stop the interview assistant"""
        self.running = False
        self.audio.terminate()
        print("✓ Interview assistant stopped successfully")

def main():
    """Main function"""
    # Gemini API key
    api_key = "AIzaSyCGG0xkcRobioTtlv52why0KcRmAZYuTTI"
    
    if not api_key:
        print("❌ Please set your Gemini API key in the script")
        return
    
    try:
        assistant = InterviewAssistant(api_key)
        assistant.start()
    except Exception as e:
        print(f"❌ Failed to start interview assistant: {e}")

if __name__ == "__main__":
    main()