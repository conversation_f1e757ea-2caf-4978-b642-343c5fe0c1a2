import requests
import json

def chat_with_ollama():
    print("🧠 Qwen3:1.7b se baat karein! (exit likhne par program band hoga)\n")

    while True:
        user_input = input("👤 Aap: ")
        if user_input.lower() in ["exit", "quit"]:
            print("👋 Chat khatam. Dhanyawaad!")
            break

        payload = {
            "model": "qwen3:1.7b",
            "messages": [
                {
                    "role": "system",
                    "content": "Please disable thinking or reasoning. Just answer briefly and directly without internal thoughts."
                },
                {
                    "role": "user",
                    "content": user_input
                }
            ]
        }

        try:
            response = requests.post("http://localhost:11434/api/chat", json=payload, stream=True)
            full_response = ""

            for line in response.iter_lines():
                if line:
                    data = json.loads(line.decode("utf-8"))
                    if "message" in data and "content" in data["message"]:
                        full_response += data["message"]["content"]

            print(f"🤖 Ollama: {full_response.strip()}\n")

        except Exception as e:
            print("⚠️ Error: Ollama server se response nahi mila.")
            print(f"Details: {e}")
            break

if __name__ == "__main__":
    chat_with_ollama()
