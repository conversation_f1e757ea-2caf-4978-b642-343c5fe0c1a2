import pyaudio
import wave
import threading
import time
import whisper
import numpy as np
from collections import deque
import sys
import os

class RealTimeTranscriber:
    def __init__(self):
        # Audio configuration
        self.CHUNK = 1024
        self.FORMAT = pyaudio.paInt16
        self.CHANNELS = 1
        self.RATE = 16000
        self.SILENCE_THRESHOLD = 500  # Adjust based on your system
        self.SILENCE_DURATION = 2.0  # Seconds of silence before processing
        self.CHUNK_DURATION = 1.0  # Process every 1 second for real-time
        
        # Initialize PyAudio
        self.audio = pyaudio.PyAudio()
        
        # Load Whisper model (you can change to 'small', 'medium', 'large')
        print("Loading Whisper model...")
        self.model = whisper.load_model("base")
        print("Model loaded successfully!")
        
        # Audio buffer
        self.audio_buffer = deque()
        self.is_recording = False
        self.last_sound_time = time.time()
        self.recording_start_time = None
        self.current_transcribed_text = ""  # Store current transcribed text
        
        # Threading
        self.recording_thread = None
        self.processing_thread = None
        self.stop_flag = threading.Event()
        
        # Temporary file for audio
        self.temp_audio_file = "temp_audio.wav"
        self.chunk_counter = 0
        
    def get_audio_devices(self):
        """List all available audio devices"""
        print("\nAvailable audio devices:")
        for i in range(self.audio.get_device_count()):
            info = self.audio.get_device_info_by_index(i)
            print(f"{i}: {info['name']} - Channels: {info['maxInputChannels']}")
    
    def is_silent(self, audio_chunk):
        """Check if audio chunk is silent"""
        audio_data = np.frombuffer(audio_chunk, dtype=np.int16)
        return np.max(np.abs(audio_data)) < self.SILENCE_THRESHOLD
    
    def process_chunk_realtime(self, audio_data):
        """Process audio chunk for real-time transcription"""
        try:
            # Save chunk to temporary WAV file
            temp_chunk_file = f"temp_chunk_{self.chunk_counter}.wav"
            self.chunk_counter += 1
            
            with wave.open(temp_chunk_file, 'wb') as wav_file:
                wav_file.setnchannels(self.CHANNELS)
                wav_file.setsampwidth(self.audio.get_sample_size(self.FORMAT))
                wav_file.setframerate(self.RATE)
                wav_file.writeframes(audio_data)
            
            # Transcribe with Whisper
            result = self.model.transcribe(temp_chunk_file, language="en")
            text = result["text"].strip()
            
            if text:
                # Update current transcribed text (concatenate/replace smartly)
                if len(text) > len(self.current_transcribed_text):
                    self.current_transcribed_text = text
                
                # Clear current line and print updated text
                print(f"\r{' ' * 100}\r", end="")  # Clear line
                print(f"🗣️  {self.current_transcribed_text}", end="", flush=True)
            
            # Clean up temporary file
            if os.path.exists(temp_chunk_file):
                os.remove(temp_chunk_file)
                
        except Exception as e:
            pass  # Ignore transcription errors for real-time chunks

    def record_audio(self):
        """Record audio from system/microphone"""
        try:
            # You might need to adjust device_index based on your system
            # Use get_audio_devices() to find the right device
            stream = self.audio.open(
                format=self.FORMAT,
                channels=self.CHANNELS,
                rate=self.RATE,
                input=True,
                frames_per_buffer=self.CHUNK,
                # input_device_index=None  # Use default input device
            )
            
            print("🎤 Started listening... (Press Ctrl+C to stop)")
            print("=" * 50)
            
            chunk_buffer = []
            last_chunk_time = time.time()
            
            while not self.stop_flag.is_set():
                try:
                    audio_chunk = stream.read(self.CHUNK, exception_on_overflow=False)
                    
                    if not self.is_silent(audio_chunk):
                        if not self.is_recording:
                            print("🔊 Sound detected, recording...")
                            self.is_recording = True
                            self.audio_buffer.clear()
                            chunk_buffer.clear()
                            self.current_transcribed_text = ""  # Reset text
                            self.recording_start_time = time.time()
                            last_chunk_time = time.time()
                        
                        self.audio_buffer.append(audio_chunk)
                        chunk_buffer.append(audio_chunk)
                        self.last_sound_time = time.time()
                        
                        # Process chunk for real-time transcription every CHUNK_DURATION seconds
                        current_time = time.time()
                        if current_time - last_chunk_time >= self.CHUNK_DURATION and chunk_buffer:
                            # Use all audio from start for better accuracy
                            all_audio_data = b''.join(self.audio_buffer)
                            # Run transcription in separate thread to avoid blocking
                            threading.Thread(target=self.process_chunk_realtime, args=(all_audio_data,), daemon=True).start()
                            last_chunk_time = current_time
                    
                    elif self.is_recording:
                        # Check if silence duration exceeded
                        if time.time() - self.last_sound_time > self.SILENCE_DURATION:
                            print(f"\n🔇 Silence detected, processing complete audio...")
                            self.process_final_audio()
                            self.is_recording = False
                            self.current_transcribed_text = ""
                            print("\n" + "=" * 50)
                            print("🎤 Listening again...")
                    
                except Exception as e:
                    print(f"Error reading audio: {e}")
                    continue
                    
        except Exception as e:
            print(f"Error opening audio stream: {e}")
            print("Try running get_audio_devices() to see available devices")
        finally:
            if 'stream' in locals():
                stream.stop_stream()
                stream.close()
    
    def process_final_audio(self):
        """Process complete recorded audio with Whisper for final result"""
        if not self.audio_buffer:
            return
        
        try:
            # Combine all audio chunks
            audio_data = b''.join(self.audio_buffer)
            
            # Save to temporary WAV file
            with wave.open(self.temp_audio_file, 'wb') as wav_file:
                wav_file.setnchannels(self.CHANNELS)
                wav_file.setsampwidth(self.audio.get_sample_size(self.FORMAT))
                wav_file.setframerate(self.RATE)
                wav_file.writeframes(audio_data)
            
            # Transcribe with Whisper for final result
            result = self.model.transcribe(self.temp_audio_file, language="en")
            
            # Clear the real-time line and print final result
            print(f"\r{' ' * 100}\r", end="")
            transcribed_text = result["text"].strip()
            if transcribed_text:
                print(f"📝 Complete Text: {transcribed_text}")
            else:
                print("❌ No speech detected")
                
        except Exception as e:
            print(f"Error processing audio: {e}")
        finally:
            # Clean up temporary file
            if os.path.exists(self.temp_audio_file):
                os.remove(self.temp_audio_file)
    
    def start(self):
        """Start the real-time transcription"""
        try:
            self.recording_thread = threading.Thread(target=self.record_audio)
            self.recording_thread.start()
            
            # Keep main thread alive
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Stopping transcription...")
            self.stop()
    
    def stop(self):
        """Stop the transcription"""
        self.stop_flag.set()
        if self.recording_thread:
            self.recording_thread.join()
        self.audio.terminate()
        print("✅ Transcription stopped")

def main():
    transcriber = RealTimeTranscriber()
    
    # Uncomment below line to see available audio devices
    # transcriber.get_audio_devices()
    
    try:
        transcriber.start()
    except Exception as e:
        print(f"Error: {e}")
        transcriber.stop()

if __name__ == "__main__":
    main()