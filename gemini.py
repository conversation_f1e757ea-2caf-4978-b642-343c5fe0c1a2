import google.generativeai as genai

API_KEY = "AIzaSyBGpgsmcwdeJfb9zcgbJ2FcflFzcEFAiYM"

def chat_with_gemini():
    genai.configure(api_key=API_KEY)

    # yahaan model ka correct ID use karein:
    model = genai.GenerativeModel('gemini-2.5-flash-lite-preview-06-17')
    chat = model.start_chat(history=[])

    print("🤖 Gemini se baat karein! (exit likhne par band ho jaayega)\n")

    while True:
        user_input = input("👤 Aap: ")
        if user_input.lower() in ["exit", "quit"]:
            print("👋 Chat khatam. Dhanyawaad!")
            break

        try:
            response = chat.send_message(user_input)
            print(f"🤖 Gemini: {response.text}\n")
        except Exception as e:
            print("⚠️ Error: Gemini se response nahi aaya.")
            print(f"Details: {e}")
            break

if __name__ == "__main__":
    chat_with_gemini()
