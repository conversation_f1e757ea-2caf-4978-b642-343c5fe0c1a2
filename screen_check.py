import pyautogui
import keyboard
import time
import base64
import io
import requests
import json
from PIL import Image
import threading
import os

class ScreenQuestionAnswerer:
    def __init__(self, gemini_api_key):
        self.api_key = gemini_api_key
        self.gemini_url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={gemini_api_key}"
        self.monitoring = False
        print("Screen Question Answerer initialized!")
        print("Caps Lock ON = Capture screen and get answer")
        print("Press Ctrl+C to exit")

    def capture_screen(self):
        """Screen capture karta hai"""
        try:
            screenshot = pyautogui.screenshot()
            return screenshot
        except Exception as e:
            print(f"Error capturing screen: {e}")
            return None

    def image_to_base64(self, image):
        """Image ko base64 format me convert karta hai"""
        try:
            buffered = io.BytesIO()
            image.save(buffered, format="PNG")
            img_str = base64.b64encode(buffered.getvalue()).decode()
            return img_str
        except Exception as e:
            print(f"Error converting image to base64: {e}")
            return None

    def ask_gemini(self, image_base64):
        """Gemini AI se question ka answer mangta hai"""
        try:
            headers = {
                'Content-Type': 'application/json',
            }
            
            payload = {
                "contents": [{
                    "parts": [
                        {
                            "text": "Please analyze this screen image and answer any question or coding problem you see. If there's a question, provide a clear and detailed answer. If it's a coding problem, provide the solution with explanation. Respond in Hindi if the question is in Hindi, otherwise respond in English."
                        },
                        {
                            "inline_data": {
                                "mime_type": "image/png",
                                "data": image_base64
                            }
                        }
                    ]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 1024,
                }
            }

            response = requests.post(self.gemini_url, headers=headers, data=json.dumps(payload))
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and len(result['candidates']) > 0:
                    answer = result['candidates'][0]['content']['parts'][0]['text']
                    return answer
                else:
                    return "No answer received from Gemini"
            else:
                return f"API Error: {response.status_code} - {response.text}"
                
        except Exception as e:
            return f"Error calling Gemini API: {e}"

    def process_screen(self):
        """Screen capture kar ke answer print karta hai"""
        print("\n🔍 Capturing screen...")
        
        # Screen capture
        screenshot = self.capture_screen()
        if not screenshot:
            print("❌ Failed to capture screen")
            return
        
        # Image ko base64 me convert
        image_base64 = self.image_to_base64(screenshot)
        if not image_base64:
            print("❌ Failed to process image")
            return
        
        print("🤖 Asking Gemini AI...")
        
        # Gemini se answer mangna
        answer = self.ask_gemini(image_base64)
        
        print("\n" + "="*60)
        print("📝 ANSWER:")
        print("="*60)
        print(answer)
        print("="*60 + "\n")

    def caps_lock_listener(self):
        """Caps Lock status monitor karta hai"""
        caps_was_on = False
        
        while self.monitoring:
            try:
                caps_is_on = keyboard.is_pressed('caps lock')
                
                # Caps Lock ON hua hai
                if caps_is_on and not caps_was_on:
                    print("🔓 Caps Lock ON - Processing screen...")
                    self.process_screen()
                
                caps_was_on = caps_is_on
                time.sleep(0.1)  # CPU usage kam karne ke liye
                
            except Exception as e:
                print(f"Error in caps lock listener: {e}")
                time.sleep(1)

    def start_monitoring(self):
        """Monitoring start karta hai"""
        self.monitoring = True
        
        # Background thread me caps lock monitor karna
        caps_thread = threading.Thread(target=self.caps_lock_listener, daemon=True)
        caps_thread.start()
        
        try:
            # Main thread ko alive rakhna
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping monitoring...")
            self.monitoring = False

def main():
    # Gemini API key input
    api_key = input("Enter your Gemini API key: ").strip()
    
    if not api_key:
        print("❌ API key required!")
        return
    
    # Screen Question Answerer start karna
    answerer = ScreenQuestionAnswerer(api_key)
    answerer.start_monitoring()

if __name__ == "__main__":
    print("📱 Screen Question Answerer")
    print("=" * 30)
    main()